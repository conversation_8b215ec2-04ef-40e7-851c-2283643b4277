/* eslint-disable @typescript-eslint/no-explicit-any */
import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';

// Import Play+ components
import { ButtonComponent } from '../../../../../play-comp-library/src/lib/components/button/button.component';
import { GlassButtonComponent } from '../../../../../play-comp-library/src/lib/components/glass-button/glass-button.component';
import { AvaTextboxComponent } from '../../../../../play-comp-library/src/lib/components/textbox/ava-textbox.component';
import { AvaTextareaComponent } from '../../../../../play-comp-library/src/lib/components/textarea/ava-textarea.component';
import { CheckboxComponent } from '../../../../../play-comp-library/src/lib/components/checkbox/checkbox.component';
import { RadioButtonComponent } from '../../../../../play-comp-library/src/lib/components/radio-button/radio-button.component';
import { CardComponent } from '../../../../../play-comp-library/src/lib/components/card/card.component';
import { AavaToggleComponent } from '../../../../../play-comp-library/src/lib/components/toggle/aava-toggle.component';
import { DropdownComponent } from '../../../../../play-comp-library/src/lib/components/dropdown/dropdown.component';
import { LinkComponent } from '../../../../../play-comp-library/src/lib/components/link/link.component';
import { BadgesComponent } from '../../../../../play-comp-library/src/lib/components/badges/badges.component';
import { TabItem } from '../../../../../play-comp-library/src/lib/components/tabs/tabs.component';
import { AavaSpinnerComponent } from '../../../../../play-comp-library/src/lib/components/spinner/aava-spinner.component';
import { ProgressComponent } from '../../../../../play-comp-library/src/lib/components/progressbar/progressbar.component';
import { AavaSliderComponent } from 'play-comp-library';

@Component({
  selector: 'ava-app-high-contrast-test',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ButtonComponent,
    GlassButtonComponent,
    AvaTextboxComponent,
    AvaTextareaComponent,
    CheckboxComponent,
    RadioButtonComponent,
    CardComponent,
    AavaToggleComponent,
    AavaSliderComponent,
    DropdownComponent,
    LinkComponent,
    BadgesComponent,
    AavaSpinnerComponent,
    ProgressComponent,
  ],
  templateUrl: './app-high-contrast-test.component.html',
  styleUrls: ['./app-high-contrast-test.component.scss'],
})
export class AppHighContrastTestComponent implements OnInit {
  // Demo data for components
  dropdownOptions = [
    { name: 'Option 1', value: '1' },
    { name: 'Option 2', value: '2' },
    { name: 'Option 3', value: '3' },
  ];

  radioOptions = [
    { label: 'Option 1', value: 'option1' },
    { label: 'Option 2', value: 'option2' },
  ];

  tabItems: TabItem[] = [
    {
      id: 'overview',
      label: 'Overview',
      content:
        '<p>High contrast mode testing overview and accessibility guidelines. Test how components adapt to high contrast settings.</p>',
      iconName: 'accessibility',
    },
    {
      id: 'components',
      label: 'Components',
      content:
        '<p>Interactive component showcase with high contrast mode applied. See how buttons, forms, cards, and other elements adapt.</p>',
      iconName: 'layers',
    },
    {
      id: 'testing',
      label: 'Testing',
      content:
        '<p>High contrast mode testing procedures and validation checklist. Ensure accessibility compliance across all components.</p>',
      iconName: 'test-tube',
    },
  ];

  activeTabId = 'overview';
  showPopup = false;
  showSnackbar = false;
  isHighContrastActive = false;

  // Form data
  formData = {
    name: '',
    email: '',
    message: '',
    country: '',
    agree: false,
    notifications: true,
    preference: 'option1',
    sliderValue: 50,
  };

  // Table data
  tableData = [
    { id: 1, name: 'John Doe', email: '<EMAIL>', status: 'Active' },
    {
      id: 2,
      name: 'Jane Smith',
      email: '<EMAIL>',
      status: 'Inactive',
    },
    { id: 3, name: 'Bob Johnson', email: '<EMAIL>', status: 'Active' },
  ];

  // List data
  listItems = [
    { text: 'First item in the list', icon: 'check' },
    { text: 'Second item with different styling', icon: 'star' },
    { text: 'Third item for testing purposes', icon: 'heart' },
  ];

  ngOnInit() {
    // Check for high contrast mode
    this.checkHighContrastMode();
  }

  checkHighContrastMode() {
    // Check if high contrast mode is active using CSS media query
    const mediaQuery = window.matchMedia('(prefers-contrast: more)');
    this.isHighContrastActive = mediaQuery.matches;

    // Listen for changes
    mediaQuery.addEventListener('change', (e) => {
      this.isHighContrastActive = e.matches;
    });
  }

  onTabChange(tabId: string) {
    this.activeTabId = tabId;
  }

  showTestPopup() {
    this.showPopup = true;
  }

  hidePopup() {
    this.showPopup = false;
  }

  showTestSnackbar() {
    this.showSnackbar = true;
    setTimeout(() => {
      this.showSnackbar = false;
    }, 3000);
  }

  onFormSubmit() {
    console.log('Form submitted:', this.formData);
    this.showTestSnackbar();
  }

  onSliderChange(value: number) {
    this.formData.sliderValue = value;
  }

  onDropdownChange(event: any) {
    this.formData.country = event.value;
  }

  onCheckboxChange(event: any) {
    this.formData.agree = event.checked;
  }

  onRadioChange(event: any) {
    this.formData.preference = event.value;
  }

  onToggleChange(event: any) {
    this.formData.notifications = event.checked;
  }
}
