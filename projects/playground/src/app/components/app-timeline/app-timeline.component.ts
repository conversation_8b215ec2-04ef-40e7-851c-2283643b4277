import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { AavaTimeline } from '../../../../../play-comp-library/src/lib/components/timeline/aava-timeline.component';


@Component({
  selector: 'app-app-timeline',
  imports: [CommonModule,AavaTimeline],
  templateUrl: './app-timeline.component.html',
  styleUrl: './app-timeline.component.scss'
})
export class AppTimelineComponent {
  // Basic timeline events
 basicEvents  = [
    {
      time: '10:00 AM',
      text: 'Project Kickoff Meeting',
      iconName: 'calendar',
      iconColor: '#3b82f6',
      iconSize: '20px',
      year: '2025',
      description: 'Initial project planning and team introduction'
    },
    {
      time: '2:00 PM',
      text: 'Development Phase Started',
      iconName: 'code',
      iconColor: '#10b981',
      iconSize: '20px',
      year: '2025',
      description: 'Begin coding and implementation'
    },
    {
      time: '4:30 PM',
      text: 'First Milestone Completed',
      iconName: 'check-circle',
      iconColor: '#f59e0b',
      iconSize: '20px',
      year: '2025',
      description: 'Successfully completed initial development goals'
    }
  ];
 
  // Events for text alignment demonstrations (includes image in icon circle example)
  alignmentEvents = [
    {
      time: '9:00 AM',
      iconName: 'users',
      iconColor: '#3b82f6',
      iconSize: '20px',
      year: '2025'

    },
    {
      time: '11:30 AM',
      iconName: 'check',
      iconColor: '#3b82f6',
      iconSize: '20px',
      year: '2025'
    },
    {
      time: '2:00 PM',
      iconName: 'search',
      iconColor: '#f59e0b',
      iconSize: '20px',
      year: '2025'
    },
    {
      time: '4:30 PM',
      iconName: 'check',
      iconColor: '#8b5cf6',
      iconSize: '20px',
      year: '2025',
    }
  ];
 


    // Events for text alignment demonstrations (includes image in icon circle example)
  alignmentEvents1 = [
    {
      time: '9:00 AM',
       imageUrl: 'assets/1.png',
      imageSize: '60px',
      year: '2025'

    },
    {
      time: '11:30 AM',
      imageUrl: 'assets/1.png',
      imageSize: '50px',
      year: '2025'
    },
    {
      time: '2:00 PM',
       imageUrl: 'assets/1.png',
      imageSize: '50px',
      year: '2025'
    },
    {
      time: '4:30 PM',
       imageUrl: 'assets/1.png',
      imageSize: '50px',
      year: '2025',
    }
  ];


   alignmentEvents2 = [
    {
      time: '9:00 AM',
      year: '2025'

    },
    {
      time: '11:30 AM',
      year: '2025'
    },
    {
      time: '2:00 PM',
      year: '2025'
    },
    {
      time: '4:30 PM',
      year: '2025',
    }
  ];
  // Events for horizontal timeline
  horizontalEvents = [
    {
      time: '9:00',
      iconName: 'play',
      iconColor: '#10b981',
      iconSize: '18px',
      year: '2025'
    },
    {
      time: '12:00',
      iconName: 'clock',
      iconColor: '#f59e0b',
      iconSize: '18px',
      year: '2025'
    },
    {
      time: '15:00',
      iconName: 'eye',
      iconColor: '#3b82f6',
      iconSize: '18px',
      year: '2025'
    },
    {
      time: '18:00',
      iconName: 'check',
      iconColor: '#ef4444',
      iconSize: '18px',
      year: '2025'
    }
  ];
 

  cardEvents = [
    {
      time: '10:00 AM',
      text: 'Project Kickoff Meeting',
      iconName: 'check',
      iconColor: '#e91e63',
      iconSize: '24px',
      year: '2025',
      title: 'Project Kickoff Meeting',
      description: 'Initial project planning session with all stakeholders',
      tags: ['meeting', 'planning', 'kickoff']
    },
    {
      time: '12:00 PM',
      text: 'Client Presentation',
      imageUrl: 'assets/avatar.jpg',
      imageSize: '30px',
      year: '2025',
      title: 'Client Presentation',
      description: 'Showcase project progress to client stakeholders',
      tags: ['presentation', 'client', 'demo']
    },
    {
      time: '3:30 PM',
      text: 'Milestone Completed',
      iconName: 'check',
      iconColor: 'green',
      iconSize: '24px',
      year: '2025',
      title: 'Development Milestone Achieved',
      description: 'Successfully completed major development milestone',
      tags: ['milestone', 'development', 'achievement']
    }
  ];
 
 
orderTrackingEvents= [
  {
    year: '2020',
    time: '15/10/2020 10:30',
    title: 'Ordered',
    description: 'Lorem ipsum dolor sit amet...',
    iconName: 'shopping-cart',
    iconColor: '#e91e63',
    showReadMore: true
  },
    {
    year: '2020',
    time: '15/10/2020 10:30',
    title: 'Ordered',
    description: 'Lorem ipsum dolor sit amet...',
    iconName: 'shopping-cart',
    iconColor: '#e91e63',
    showReadMore: true
  },
    {
    year: '2020',
    time: '15/10/2020 10:30',
    title: 'Ordered',
    description: 'Lorem ipsum dolor sit amet...',
    iconName: 'shopping-cart',
    iconColor: '#e91e63',
    showReadMore: true
  }
];



cardEvents1= [
    {
      title: 'Product Launch Event',
      description: 'Successfully launched our new product line with great customer response',
      year: '2023',
      time: '10:00 AM',
      iconName: 'check',
      iconColor:'black',
      cardImageUrl: 'https://images.unsplash.com/photo-1526779259212-939e64788e3c?fm=jpg&q=60&w=3000&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxzZWFyY2h8M3x8ZnJlZSUyMGltYWdlc3xlbnwwfHwwfHx8MA%3D%3D',
      cardImageAlt: 'Product Launch Event'
    },
    {
      title: 'Team Building Workshop',
      description: 'Organized a comprehensive team building workshop to improve collaboration',
      year: '2023',
      time: '12:00 PM',
      iconName: 'check',
      iconColor:'black',
      cardImageUrl: 'https://media.istockphoto.com/id/517188688/photo/mountain-landscape.jpg?s=1024x1024&w=0&k=20&c=z8_rWaI8x4zApNEEG9DnWlGXyDIXe-OmsAyQ5fGPVV8=',
      cardImageAlt: 'Team Building Workshop'
    },
    {
      title: 'Quarterly Review Meeting',
      description: 'Conducted quarterly performance review and planning session',
      year: '2023',
      time: '2:00 PM',
      iconName: 'check',
      iconColor:'black',
      cardImageUrl: 'https://t4.ftcdn.net/jpg/02/56/10/07/360_F_256100731_qNLp6MQ3FjYtA3Freu9epjhsAj2cwU9c.jpg',
      cardImageAlt: 'Quarterly Review Meeting'
    }
  ];
}
