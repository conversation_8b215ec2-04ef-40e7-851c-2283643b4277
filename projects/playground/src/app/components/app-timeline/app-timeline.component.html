<div class="timeline-demo-container">
 
  <!-- Basic Timeline Examples -->
  <section class="demo-section">
    <h2>Basic Timeline Examples</h2>
 
    <h3>Ascending Timeline</h3>
    <aava-timeline class="vertical"
      [events]="alignmentEvents"
      [sortOrder]="'ascending'"
      orientation="vertical"
      textAlign="zigzag-left">
    </aava-timeline>
 
    <h3>Descending Timeline</h3>
     <aava-timeline
      [events]="alignmentEvents"
      [sortOrder]="'descending'"
      orientation="vertical"
      textAlign="zigzag-right"
      [iconCircleSize]="'30px'">
    </aava-timeline>
  </section>
 
  <!-- Text Alignment Examples - Vertical -->
  <section class="demo-section">
    <h2>Vertical Timeline - Zigzag Layout with Text Alignment Examples</h2>
 
    <h3>Zigzag Layout - All Text Left Side</h3>
    <aava-timeline
      [events]="basicEvents"
      orientation="vertical"
      textAlign="zigzag-left"
      [iconCircleSize]="'45px'">
    </aava-timeline>
 
    <h3>Zigzag Layout - All Text Right Side</h3>
    <aava-timeline
      [events]="basicEvents"
      orientation="vertical"
      textAlign="zigzag-right"
      [iconCircleSize]="'35px'">
    </aava-timeline>
 
    <h3>Zigzag Layout - Alternating Text (Left/Right)</h3>
    <aava-timeline
      [events]="basicEvents"
      orientation="vertical"
      textAlign="zig-zag"
      [iconCircleSize]="'40px'">
    </aava-timeline>
  </section>
 
  <!-- Text Alignment Examples - Horizontal -->
  <section class="demo-section">
    <h2 class="horizontal-heading">Horizontal Timeline - Zigzag Layout with Text Alignment Examples</h2>
 
    <section class="horizontal-timeline-demo">
    <h3>Zigzag Layout - All Text Above</h3>
    <aava-timeline
      [events]="horizontalEvents"
      orientation="horizontal"
      textAlign="zigzag-up"
      [iconCircleSize]="'45px'">
    </aava-timeline>
    </section>
 

    <section class="horizontal-timeline-demo">
    <h3>Zigzag Layout - All Text Below</h3>
    <aava-timeline
      [events]="horizontalEvents"
      orientation="horizontal"
      textAlign="zigzag-down"
      [iconCircleSize]="'35px'">
    </aava-timeline>
    </section>
  
    <section class="horizontal-timeline-demo">
    <h3>Zigzag Layout - Alternating Text (Up/Down)</h3>
    <aava-timeline
      [events]="horizontalEvents"
      orientation="horizontal"
      textAlign="zig-zag"
      [iconCircleSize]="'40px'">
    </aava-timeline>
    </section>
  </section>
 

 
 
<section class="demo-section">
  <h2>Custom Icon Circle Colors & Image Examples</h2>

  <h3>Icon Circle with Custom Colors</h3>
    <aava-timeline
      [events]="alignmentEvents"
      [sortOrder]="'ascending'"
      orientation="vertical"
      textAlign="zigzag-left"
      [iconCircleSize]="'40px'"
      [iconCircleBgColor]="'green'"></aava-timeline>
     
      <h3>Icon Circle with Image</h3>
      <aava-timeline
      [events]="alignmentEvents1"
      [sortOrder]="'ascending'"
      [iconCircleSize]="'50px'"
      orientation="vertical"
      textAlign="zigzag-right">
    </aava-timeline>

    <h3>Icon Circle with Custom Colors</h3>
    <aava-timeline
      [events]="alignmentEvents2"
      [sortOrder]="'ascending'"
      orientation="vertical"
      textAlign="zigzag-left"
       [iconCircleSize]="'20px'"
      [iconCircleBgColor]="'black'">
    </aava-timeline>
</section>
 
<section>
  <h3>Event Timeline with Images Inside Cards</h3>
  <p><em>Each card contains an image related to the event content ({{ cardEvents1.length }} events)</em></p>
  <aava-timeline
    [events]="cardEvents1"
    [sortOrder]="'ascending'"
    [orientation]="'vertical'"
    textAlign="zig-zag"
    [card]="true"
    [cardTemplate]="simpleCardTemplate">
     <ng-template #simpleCardTemplate let-event let-index="index">
    <div class="simple-card-content">
      <div class="card-image-container" *ngIf="event.cardImageUrl">
        <img
          [src]="event.cardImageUrl"
          [alt]="event.cardImageAlt || 'Event image'"
          class="card-image"
          style="width: 100%; height: 150px; object-fit: cover; border-radius: 8px; margin-bottom: 12px;">
      </div>
      <h4>{{ event.title }}</h4>
      <p>{{ event.description }}</p>
      <small>{{ event.year }} - {{ event.time }}</small>
      <div class="event-number">Event #{{ index + 1 }} of {{ cardEvents1.length }}</div>
    </div>
  </ng-template>
  </aava-timeline>
</section>

<section>
  <h3>Simple Event Timeline - Individual Cards (Zigzag-Left with Dynamic Spacing)</h3>
  <p><em>All cards aligned to the left with dynamic spacing to prevent overlap ({{ cardEvents.length }} events)</em></p>
  <aava-timeline
    [events]="cardEvents"
    [sortOrder]="'ascending'"
    [orientation]="'vertical'"
    textAlign="zigzag-left"
    [card]="true"
    [cardTemplate]="simpleCardTemplate">
     <ng-template #simpleCardTemplate let-event let-index="index">
    <div class="simple-card-content">
      <h4>{{ event.title }}</h4>
      <p>{{ event.description }}</p>
      <small>{{ event.year }} - {{ event.time }}</small>
      <div class="event-number">Event #{{ index + 1 }}</div>
    </div>
  </ng-template> 
  </aava-timeline>
  </section>

<section>
  <h3>Event Timeline with Images Inside Cards</h3>
  <p><em>Each card contains an image related to the event content ({{ cardEvents1.length }} events)</em></p>
  <aava-timeline
    [events]="cardEvents1"
    [sortOrder]="'ascending'"
    [orientation]="'vertical'"
    textAlign="zigzag-right"
    [card]="true"
    [cardTemplate]="simpleCardTemplate">
     <ng-template #simpleCardTemplate let-event let-index="index">
    <div class="simple-card-content">
      <div class="card-image-container" *ngIf="event.cardImageUrl">
        <img
          [src]="event.cardImageUrl"
          [alt]="event.cardImageAlt || 'Event image'"
          class="card-image"
          style="width: 100%; height: 150px; object-fit: cover; border-radius: 8px; margin-bottom: 12px;">
      </div>
      <h4>{{ event.title }}</h4>
      <p>{{ event.description }}</p>
      <small>{{ event.year }} - {{ event.time }}</small>
      <div class="event-number">Event #{{ index + 1 }} of {{ cardEvents1.length }}</div>
    </div>
  </ng-template>
  </aava-timeline>
</section>