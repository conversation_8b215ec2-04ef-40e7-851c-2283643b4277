import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import {
  ThemeService,
  Theme,
  ThemeInfo,
} from '../../../../../play-comp-library/src/lib/services/theme.service';

// Import Play+ components
import { ButtonComponent } from '../../../../../play-comp-library/src/lib/components/button/button.component';
import { AvaTextboxComponent } from '../../../../../play-comp-library/src/lib/components/textbox/ava-textbox.component';
import { AvaTextareaComponent } from '../../../../../play-comp-library/src/lib/components/textarea/ava-textarea.component';
import { CheckboxComponent } from '../../../../../play-comp-library/src/lib/components/checkbox/checkbox.component';
import { RadioButtonComponent } from '../../../../../play-comp-library/src/lib/components/radio-button/radio-button.component';
import { CardComponent } from '../../../../../play-comp-library/src/lib/components/card/card.component';
import { AavaToggleComponent } from '../../../../../play-comp-library/src/lib/components/toggle/aava-toggle.component';
import { AavaSliderComponent } from '../../../../../play-comp-library/src/lib/components/slider/aava-slider.component';
import { DropdownComponent } from '../../../../../play-comp-library/src/lib/components/dropdown/dropdown.component';
import { LinkComponent } from '../../../../../play-comp-library/src/lib/components/link/link.component';
import { BadgesComponent } from '../../../../../play-comp-library/src/lib/components/badges/badges.component';
import {
  TabsComponent,
  TabItem,
} from '../../../../../play-comp-library/src/lib/components/tabs/tabs.component';
import { AavaSpinnerComponent } from '../../../../../play-comp-library/src/lib/components/spinner/aava-spinner.component';
import { ProgressComponent } from '../../../../../play-comp-library/src/lib/components/progressbar/progressbar.component';
import { AvatarsComponent } from '../../../../../play-comp-library/src/lib/components/avatars/avatars.component';

/* eslint-disable @angular-eslint/component-selector */
@Component({
  selector: 'app-theme-preview',
  standalone: true,
  imports: [
    CommonModule,
    ButtonComponent,
    AvaTextboxComponent,
    AvaTextareaComponent,
    CheckboxComponent,
    RadioButtonComponent,
    CardComponent,
    AavaToggleComponent,
    AavaSliderComponent,
    DropdownComponent,
    LinkComponent,
    BadgesComponent,
    TabsComponent,
    AavaSpinnerComponent,
    ProgressComponent,
    AvatarsComponent,
  ],
  templateUrl: './app-theme-preview.component.html',
  styleUrls: ['./app-theme-preview.component.scss'],
})
export class AppThemePreviewComponent implements OnInit {
  loading = true;
  currentTheme: Theme = 'default';
  themes: ThemeInfo[] = [];

  // Demo data for components
  dropdownOptions = [
    { name: 'Option 1', value: '1' },
    { name: 'Option 2', value: '2' },
    { name: 'Option 3', value: '3' },
  ];

  radioOptions = [
    { label: 'Option 1', value: 'option1' },
    { label: 'Option 2', value: 'option2' },
  ];

  tabItems: TabItem[] = [
    {
      id: 'overview',
      label: 'Overview',
      content:
        '<p>Theme overview and general information about the design system. Explore how different themes affect the visual appearance of components.</p>',
      iconName: 'home',
    },
    {
      id: 'components',
      label: 'Components',
      content:
        '<p>Interactive component showcase with current theme applied. See how buttons, forms, cards, and other elements adapt to theme changes.</p>',
      iconName: 'layers',
    },
    {
      id: 'settings',
      label: 'Settings',
      content:
        '<p>Theme configuration and customization options. Switch between different theme variants and see real-time changes.</p>',
      iconName: 'settings',
    },
  ];

  activeTabId = 'overview';

  accordionItems = [
    { title: 'Accordion Item 1', content: 'Content for accordion item 1' },
    { title: 'Accordion Item 2', content: 'Content for accordion item 2' },
    { title: 'Accordion Item 3', content: 'Content for accordion item 3' },
  ];

  constructor(private themeService: ThemeService) { }

  ngOnInit() {
    this.loading = true;
    this.themes = Object.values(this.themeService.getAllThemes());
    this.currentTheme = this.themeService.getCurrentTheme();
    this.loading = false;
  }

  setTheme(theme: Theme) {
    this.themeService.setTheme(theme);
    this.currentTheme = theme;
  }

  toggleTheme() {
    this.themeService.toggleTheme();
    this.currentTheme = this.themeService.getCurrentTheme();
  }

  cycleTheme() {
    this.themeService.cycleTheme();
    this.currentTheme = this.themeService.getCurrentTheme();
  }

  getCurrentThemeInfo(): ThemeInfo {
    return this.themeService.getCurrentThemeInfo();
  }

  isThemeActive(themeName: Theme): boolean {
    return this.currentTheme === themeName;
  }

  getThemeArray(): ThemeInfo[] {
    return this.themes;
  }

  getThemesByCategory(category: 'default' | 'dark' | 'acme'): ThemeInfo[] {
    return this.themeService.getThemesByCategory(category);
  }

  getUseCase(themeName: Theme): string {
    const useCases: Record<Theme, string> = {
      default: 'General applications, marketing sites',
      light: 'Daytime interfaces, productivity apps',
      'modern-vibrant': 'Creative platforms, modern apps',
      dark: 'Nighttime interfaces, media apps',
      console: 'Developer tools, code editors',
      acme: 'Professional business platforms',
      enterprise: 'Corporate applications, B2B',
      corporate: 'Formal business environments',
    };
    return useCases[themeName] || 'General purpose';
  }
}
