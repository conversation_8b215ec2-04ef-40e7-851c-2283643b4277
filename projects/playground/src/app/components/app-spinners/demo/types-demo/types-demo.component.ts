import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import {
  AavaSpinnerComponent,
  SpinnerType,
} from '../../../../../../../play-comp-library/src/lib/components/spinner/aava-spinner.component';

@Component({
  selector: 'ava-types-demo',
  standalone: true,
  imports: [CommonModule, AavaSpinnerComponent],
  templateUrl: './types-demo.component.html',
  styleUrls: ['./types-demo.component.scss'],
})
export class TypesDemoComponent {
  spinnerTypes: SpinnerType[] = [
    'circular',
    'dotted',
    'partial',
    'gradient',
    'dashed',
  ];

  copyToClipboard(): void {
    const code = this.getExampleCode();
    navigator.clipboard
      .writeText(code)
      .then(() => {
        console.log('Code copied to clipboard');
      })
      .catch((err) => {
        console.error('Failed to copy code:', err);
      });
  }

  getExampleCode(): string {
    return `import { Component } from '@angular/core';
import { SpinnerComponent, SpinnerType } from '../../../../../../../play-comp-library/src/lib/components/spinner/spinner.component';

@Component({
  selector: 'app-spinner-types',
  standalone: true,
  imports: [SpinnerComponent],
  template: \`
    <div class="spinner-types-demo">
      <div class="spinner-grid">
        <div class="spinner-item" *ngFor="let type of spinnerTypes">
          <ava-spinner [type]="type" color="primary" size="md" [animation]="true"></ava-spinner>
          <span class="type-label">{{ type | titlecase }}</span>
        </div>
      </div>
    </div>
  \`
})
export class SpinnerTypesComponent {
  spinnerTypes: SpinnerType[] = ['circular', 'dotted', 'partial', 'gradient', 'dashed', 'double'];
}`;
  }
}
