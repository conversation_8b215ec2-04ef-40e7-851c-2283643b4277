import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { AavaSpinnerComponent } from '../../../../../../../play-comp-library/src/lib/components/spinner/aava-spinner.component';

@Component({
  selector: 'ava-api-demo',
  standalone: true,
  imports: [CommonModule, AavaSpinnerComponent],
  templateUrl: './api-demo.component.html',
  styleUrls: ['./api-demo.component.scss'],
})
export class ApiDemoComponent {
  apiProps = [
    {
      name: 'type',
      type: "'circular' | 'dotted' | 'partial' | 'gradient' | 'dashed'",
      default: "'circular'",
      description: 'Visual style of the spinner',
    },
    {
      name: 'size',
      type: "'sm' | 'md' | 'lg' | 'xl'",
      default: "'md'",
      description: 'Size of the spinner',
    },
    {
      name: 'color',
      type: "'primary' | 'secondary' | 'success' | 'warning' | 'danger'",
      default: "'primary'",
      description: 'Color variant of the spinner',
    },
    {
      name: 'animation',
      type: 'boolean',
      default: 'true',
      description: 'Whether to animate the spinner',
    },
    {
      name: 'progressIndex',
      type: 'number',
      default: 'undefined',
      description: 'Progress value for determinate loading (0-100)',
    },
    {
      name: 'className',
      type: 'string',
      default: "''",
      description: 'Additional CSS classes to apply',
    },
  ];

  copyToClipboard(): void {
    const code = this.getExampleCode();
    navigator.clipboard
      .writeText(code)
      .then(() => {
        console.log('Code copied to clipboard');
      })
      .catch((err) => {
        console.error('Failed to copy code:', err);
      });
  }

  getExampleCode(): string {
    return `import { Component } from '@angular/core';
import { AavaSpinnerComponent } from '../../../../../../../play-comp-library/src/lib/components/spinner/aava-spinner.component';

@Component({
  selector: 'app-spinner-api',
  standalone: true,
  imports: [AavaSpinnerComponent],
  template: \`
    <!-- Basic usage -->
    <aava-spinner type="circular" size="md" color="primary"></aava-spinner>
    
    <!-- With progress -->
    <aava-spinner type="circular" [progressIndex]="75" color="success"></aava-spinner>
    
    <!-- Without animation -->
    <aava-spinner type="dotted" [animation]="false" color="warning"></aava-spinner>
    
    <!-- Custom class -->
    <aava-spinner type="gradient" className="custom-spinner" color="primary"></aava-spinner>
  \`
})
export class SpinnerApiComponent { }`;
  }
}
