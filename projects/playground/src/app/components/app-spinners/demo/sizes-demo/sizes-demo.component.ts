import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import {
  AavaSpinnerComponent,
  SpinnerSize,
} from '../../../../../../../play-comp-library/src/lib/components/spinner/aava-spinner.component';

@Component({
  selector: 'ava-sizes-demo',
  standalone: true,
  imports: [CommonModule, AavaSpinnerComponent],
  templateUrl: './sizes-demo.component.html',
  styleUrls: ['./sizes-demo.component.scss'],
})
export class SizesDemoComponent {
  spinnerSizes: SpinnerSize[] = ['xs', 'sm', 'md', 'lg', 'xl'];

  copyToClipboard(): void {
    const code = this.getExampleCode();
    navigator.clipboard
      .writeText(code)
      .then(() => {
        console.log('Code copied to clipboard');
      })
      .catch((err) => {
        console.error('Failed to copy code:', err);
      });
  }

  getExampleCode(): string {
    return `import { Component } from '@angular/core';
import { SpinnerComponent, SpinnerSize } from '../../../../../../../play-comp-library/src/lib/components/spinner/spinner.component';

@Component({
  selector: 'app-spinner-sizes',
  standalone: true,
  imports: [SpinnerComponent],
  template: \`
    <div class="spinner-sizes-demo">
      <div class="size-row">
        <div class="size-item" *ngFor="let size of spinnerSizes">
          <ava-spinner type="circular" color="primary" [size]="size" [animation]="true"></ava-spinner>
          <span class="size-label">{{ size | uppercase }}</span>
        </div>
      </div>
    </div>
  \`
})
export class SpinnerSizesComponent {
  spinnerSizes: SpinnerSize[] = ['sm', 'md', 'lg', 'xl'];
}`;
  }
}
