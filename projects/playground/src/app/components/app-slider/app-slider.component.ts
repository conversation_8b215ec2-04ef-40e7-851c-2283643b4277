import {
  Component,
  signal,
  ViewEncapsulation,
  WritableSignal,
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { AavaSliderComponent } from '../../../../../play-comp-library/src/lib/components/slider/aava-slider.component';

interface SliderDocSection {
  title: string;
  description: string;
  showCode: boolean;
}

interface ApiProperty {
  name: string;
  type: string;
  default: string;
  description: string;
}

@Component({
  selector: 'ava-app-slider',
  standalone: true,
  imports: [CommonModule, RouterModule, AavaSliderComponent],
  templateUrl: './app-slider.component.html',
  styleUrls: ['./app-slider.component.scss'],
  encapsulation: ViewEncapsulation.None,
})
export class AppSliderComponent {
  sections: SliderDocSection[] = [
    {
      title: 'Basic Usage',
      description: 'Simple slider with default configuration.',
      showCode: false,
    },
    {
      title: 'Custom Range',
      description: 'Sliders with custom minimum, maximum, and step values.',
      showCode: false,
    },
    {
      title: 'Keyboard Navigation',
      description: 'Slider with keyboard navigation support.',
      showCode: false,
    },
    {
      title: 'Decimal Steps',
      description: 'Slider with decimal step values.',
      showCode: false,
    },
    {
      title: 'Small Range',
      description: 'Slider with a small range of values.',
      showCode: false,
    },
    {
      title: 'Large Range',
      description: 'Slider with a large range of values.',
      showCode: false,
    },
    {
      title: 'Tooltip Visible',
      description: 'Slider with tooltip visibility enabled.',
      showCode: false,
    },
    {
      title: 'Tooltip Hidden',
      description: 'Slider with tooltip visibility disabled.',
      showCode: false,
    },
    {
      title: 'Multi Range Slider',
      description: 'Slider with two handles for selecting a range.',
      showCode: false,
    },
    {
      title: 'Slider with Handle Icon',
      description: 'Slider with an icon inside the handle (bulb).',
      showCode: false,
    },
  ];

  // API Documentation
  apiProps: ApiProperty[] = [
    {
      name: 'min',
      type: 'number',
      default: '0',
      description: 'Minimum value of the slider.',
    },
    {
      name: 'max',
      type: 'number',
      default: '100',
      description: 'Maximum value of the slider.',
    },
    {
      name: 'value',
      type: 'number',
      default: '0',
      description: 'Current value of the slider.',
    },
    {
      name: 'step',
      type: 'number',
      default: '1',
      description: 'Step value of the slider.',
    },
    {
      name: 'showTooltip',
      type: 'boolean',
      default: 'true',
      description: 'Whether to show the tooltip.',
    },
  ];

  // Example state management
  basicValue: WritableSignal<number> = signal(50);
  temperatureValue: WritableSignal<number> = signal(22);
  volumeValue: WritableSignal<number> = signal(75);
  progressValue: WritableSignal<number> = signal(35);
  accessibleValue: WritableSignal<number> = signal(65);
  decimalStepValue: WritableSignal<number> = signal(1);
  smallRangeValue: WritableSignal<number> = signal(5);
  largeRangeValue: WritableSignal<number> = signal(500);
  tooltipVisibleValue: WritableSignal<number> = signal(50);
  tooltipHiddenValue: WritableSignal<number> = signal(50);

  // Multi Range Slider state
  multiMinValue: WritableSignal<number> = signal(20);
  multiMaxValue: WritableSignal<number> = signal(80);
  onMultiMinValueChange(value: number): void {
    this.multiMinValue.set(value);
  }
  onMultiMaxValueChange(value: number): void {
    this.multiMaxValue.set(value);
  }

  // Icon Slider state
  handleIconSliderValue: WritableSignal<number> = signal(40);
  onHandleIconSliderValueChange(value: number): void {
    this.handleIconSliderValue.set(value);
  }

  toggleCodeVisibility(index: number, event: MouseEvent | Event): void {
    event.stopPropagation();
    this.sections[index].showCode = !this.sections[index].showCode;
  }

  onSliderChange(value: number): void {
    console.log('Slider value:', value);
  }

  // Specific event handlers for each slider
  onBasicValueChange(value: number): void {

    this.basicValue.set(value);
    console.log('Basic slider value changed:', value);
  }

  onTemperatureChange(value: number): void {
    this.temperatureValue.set(value);
    console.log('Temperature changed:', value + '°C');
  }

  onVolumeChange(value: number): void {
    this.volumeValue.set(value);
    console.log('Volume changed:', value + '%');
  }

  onProgressChange(value: number): void {
    this.progressValue.set(value);
    console.log('Progress changed:', value + '%');
  }

  onAccessibleValueChange(value: number): void {
    this.accessibleValue.set(value);
    console.log('Accessible slider value changed:', value);
  }

  onDecimalStepChange(value: number): void {
    this.decimalStepValue.set(value);
    console.log('Decimal step slider value changed:', value);
  }

  onSmallRangeChange(value: number): void {
    this.smallRangeValue.set(value);
    console.log('Small range slider value changed:', value);
  }

  onLargeRangeChange(value: number): void {
    this.largeRangeValue.set(value);
    console.log('Large range slider value changed:', value);
  }

  onTooltipVisibleChange(value: number): void {
    this.tooltipVisibleValue.set(value);
    console.log('Tooltip visible slider value changed:', value);
  }

  onTooltipHiddenChange(value: number): void {
    this.tooltipHiddenValue.set(value);
    console.log('Tooltip hidden slider value changed:', value);
  }

  onProgressUpdate(): void {
    const current = this.progressValue();
    if (current < 100) {
      this.progressValue.set(Math.min(100, current + 10));
    } else {
      this.progressValue.set(0);
    }
    console.log('Progress updated to:', this.progressValue());
  }

  copyCode(section: string): void {
    const code = this.getExampleCode(section);
    navigator.clipboard
      .writeText(code)
      .then(() => {
        console.log('Code copied to clipboard');
      })
      .catch((err) => {
        console.error('Failed to copy code:', err);
      });
  }

  getExampleCode(section: string): string {
    const examples: Record<string, string> = {
      'basic usage': `// Basic slider example
import { Component, signal } from '@angular/core';
import { SliderComponent } from '@awe/play-comp-library';

@Component({
  selector: 'app-basic-slider',
  standalone: true,
  imports: [SliderComponent],
  template: \`
    <div class="slider-container">
      <ava-slider
        [value]="basicValue()"
        (valueChange)="basicValue.set($event)"
      ></ava-slider>
    </div>
  \`
})
export class BasicSliderComponent {
  basicValue = signal(50);
}`,
      'custom range': `// Custom range slider example
import { Component, signal } from '@angular/core';
import { SliderComponent } from '@awe/play-comp-library';

@Component({
  selector: 'app-custom-range-slider',
  standalone: true,
  imports: [SliderComponent],
  template: \`
    <div class="range-container">
      <ava-slider
        [value]="temperatureValue()"
        [min]="0"
        [max]="40"
        (valueChange)="temperatureValue.set($event)"
      ></ava-slider>
    </div>
  \`
})
export class CustomRangeSliderComponent {
  temperatureValue = signal(22);
}`,
      'keyboard navigation': `// Keyboard accessible slider
import { Component, signal } from '@angular/core';
import { SliderComponent } from '@awe/play-comp-library';

@Component({
  selector: 'app-accessible-slider',
  standalone: true,
  imports: [SliderComponent],
  template: \`
    <div class="accessible-container">
      <ava-slider
        [value]="accessibleValue()"
        [min]="0"
        [max]="100"
        (valueChange)="accessibleValue.set($event)"
      ></ava-slider>
    </div>
  \`
})
export class AccessibleSliderComponent {
  accessibleValue = signal(65);
}`,
      'decimal steps': `// Decimal step slider example
import { Component, signal } from '@angular/core';
import { SliderComponent } from '@awe/play-comp-library';

@Component({
  selector: 'app-decimal-step-slider',
  standalone: true,
  imports: [SliderComponent],
  template: \`
    <div class="decimal-step-container">
      <ava-slider
        [value]="decimalStepValue()"
        [min]="0"
        [max]="1"
        [step]="0.1"
        (valueChange)="decimalStepValue.set($event)"
      ></ava-slider>
    </div>
  \`
})
export class DecimalStepSliderComponent {
  decimalStepValue = signal(1);
}`,
      'small range': `// Small range slider example
import { Component, signal } from '@angular/core';
import { SliderComponent } from '@awe/play-comp-library';

@Component({
  selector: 'app-small-range-slider',
  standalone: true,
  imports: [SliderComponent],
  template: \`
    <div class="small-range-container">
      <ava-slider
        [value]="smallRangeValue()"
        [min]="0"
        [max]="10"
        (valueChange)="smallRangeValue.set($event)"
      ></ava-slider>
    </div>
  \`
})
export class SmallRangeSliderComponent {
  smallRangeValue = signal(5);
}`,
      'large range': `// Large range slider example
import { Component, signal } from '@angular/core';
import { SliderComponent } from '@awe/play-comp-library';

@Component({
  selector: 'app-large-range-slider',
  standalone: true,
  imports: [SliderComponent],
  template: \`
    <div class="large-range-container">
      <ava-slider
        [value]="largeRangeValue()"
        [min]="0"
        [max]="1000"
        [step]="10"
        (valueChange)="largeRangeValue.set($event)"
      ></ava-slider>
    </div>
  \`
})
export class LargeRangeSliderComponent {
  largeRangeValue = signal(500);
}`,
      'tooltip visible': `// Tooltip visible slider example
import { Component, signal } from '@angular/core';
import { SliderComponent } from '@awe/play-comp-library';

@Component({
  selector: 'app-tooltip-visible-slider',
  standalone: true,
  imports: [SliderComponent],
  template: \`
    <div class="tooltip-visible-container">
      <ava-slider
        [value]="tooltipVisibleValue()"
        [showTooltip]="true"
        (valueChange)="tooltipVisibleValue.set($event)"
      ></ava-slider>
    </div>
  \`
})
export class TooltipVisibleSliderComponent {
  tooltipVisibleValue = signal(50);
}`,
      'tooltip hidden': `// Tooltip hidden slider example
import { Component, signal } from '@angular/core';
import { SliderComponent } from '@awe/play-comp-library';

@Component({
  selector: 'app-tooltip-hidden-slider',
  standalone: true,
  imports: [SliderComponent],
  template: \`
    <div class="tooltip-hidden-container">
      <ava-slider
        [value]="tooltipHiddenValue()"
        [showTooltip]="false"
        (valueChange)="tooltipHiddenValue.set($event)"
      ></ava-slider>
    </div>
  \`
})
export class TooltipHiddenSliderComponent {
  tooltipHiddenValue = signal(50);
}`,
    };
    return examples[section.toLowerCase().replace(/\s+/g, '')] || '';
  }
}
