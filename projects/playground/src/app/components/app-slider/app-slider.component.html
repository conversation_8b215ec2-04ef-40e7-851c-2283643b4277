<div class="documentation">
  <!-- Demo Navigation -->
  <div class="demo-navigation">
    <h3>Interactive Demos</h3>
    <div class="demo-links">
      <a routerLink="/slider/basic-usage" class="demo-link">Basic Usage</a>
      <a routerLink="/slider/orientations" class="demo-link">Orientations</a>
      <a routerLink="/slider/sizes" class="demo-link">Sizes</a>
      <a routerLink="/slider/input-variant" class="demo-link">Input Variant</a>
      <a routerLink="/slider/states" class="demo-link">States</a>
      <a routerLink="/slider/multi-range" class="demo-link">Multi-Range</a>
      <a routerLink="/slider/icon-thumb" class="demo-link">Icon Thumb</a>
      <a routerLink="/slider/api" class="demo-link">API Reference</a>
    </div>
  </div>

  <!-- Header -->
  <div class="row">
    <div class="col-12">
      <header class="doc-header">
        <h1>Slider Component</h1>
        <p class="description">
          A simple and accessible slider component for selecting numeric values
          within a range.
        </p>
      </header>
    </div>
  </div>

  <!-- Installation -->
  <div class="row">
    <div class="col-12">
      <section class="doc-section">
        <h2>Installation</h2>
        <div class="code-block">
          <pre><code>import {{ '{' }} SliderComponent {{ '}' }} from '&#64;awe/play-comp-library';</code></pre>
        </div>
      </section>
    </div>
  </div>

  <!-- Documentation Sections -->
  <div class="doc-sections">
    <section *ngFor="let section of sections; let i = index" class="doc-section">
      <div class="row">
        <div class="col-12">
          <div class="section-header" tabindex="0" role="button">
            <h2>{{ section.title }}</h2>
            <div class="description-container">
              <p>{{ section.description }}</p>
              <div class="code-toggle" tabindex="0" (click)="toggleCodeVisibility(i, $event)"
                (keydown.enter)="toggleCodeVisibility(i, $event)" (keydown.space)="toggleCodeVisibility(i, $event)"
                role="button">
                <span *ngIf="!section.showCode">View Code</span>
                <span *ngIf="section.showCode">Hide Code</span>
                <!-- <awe-icons [iconName]="section.showCode ? 'awe_arrow_back_up' : 'awe_arrow_back_down'" iconColor="action"></awe-icons> -->
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Code Example -->
      <div class="code-example" [class.expanded]="section.showCode">
        <div class="example-preview">
          <ng-container [ngSwitch]="section.title">
            <!-- Basic Usage -->
            <ng-container *ngSwitchCase="'Basic Usage'">
              <div class="row g-3">
                <div class="col-12">
                  <aava-slider [value]="basicValue()" (valueChange)="onBasicValueChange($event)">
                  </aava-slider>
                </div>
              </div>
            </ng-container>

            <!-- Custom Range -->
            <ng-container *ngSwitchCase="'Custom Range'">
              <div class="row g-3">
                <div class="col-12">
                  <aava-slider [value]="temperatureValue()" [min]="0" [max]="40"
                    (valueChange)="onTemperatureChange($event)">
                  </aava-slider>
                </div>
              </div>
            </ng-container>

            <!-- Keyboard Navigation -->
            <ng-container *ngSwitchCase="'Keyboard Navigation'">
              <div class="row g-3">
                <div class="col-12">
                  <aava-slider [value]="accessibleValue()" [min]="0" [max]="100"
                    (valueChange)="onAccessibleValueChange($event)">
                  </aava-slider>
                  <p class="keyboard-info">
                    Use arrow keys to adjust value, Home/End for min/max values.
                  </p>
                </div>
              </div>
            </ng-container>

            <!-- Decimal Steps -->
            <ng-container *ngSwitchCase="'Decimal Steps'">
              <div class="row g-3">
                <div class="col-12">
                  <aava-slider [value]="1" [min]="0" [max]="1" [step]="0.1" (valueChange)="onDecimalStepChange($event)">
                  </aava-slider>
                </div>
              </div>
            </ng-container>

            <!-- Small Range -->
            <ng-container *ngSwitchCase="'Small Range'">
              <div class="row g-3">
                <div class="col-12">
                  <aava-slider [value]="5" [min]="0" [max]="10" [step]="1" (valueChange)="onSmallRangeChange($event)">
                  </aava-slider>
                </div>
              </div>
            </ng-container>

            <!-- Large Range -->
            <ng-container *ngSwitchCase="'Large Range'">
              <div class="row g-3">
                <div class="col-12">
                  <aava-slider [value]="500" [min]="0" [max]="1000" [step]="10"
                    (valueChange)="onLargeRangeChange($event)">
                  </aava-slider>
                </div>
              </div>
            </ng-container>

            <!-- Tooltip Visible -->
            <ng-container *ngSwitchCase="'Tooltip Visible'">
              <div class="row g-3">
                <div class="col-12">
                  <aava-slider [value]="50" [showTooltip]="true" (valueChange)="onTooltipVisibleChange($event)">
                  </aava-slider>
                </div>
              </div>
            </ng-container>

            <!-- Tooltip Hidden -->
            <ng-container *ngSwitchCase="'Tooltip Hidden'">
              <div class="row g-3">
                <div class="col-12">
                  <aava-slider [value]="50" [showTooltip]="false" (valueChange)="onTooltipHiddenChange($event)">
                  </aava-slider>
                </div>
              </div>
            </ng-container>

            <!-- Multi Range Slider -->
            <ng-container *ngSwitchCase="'Multi Range Slider'">
              <div class="row g-3">
                <div class="col-12">
                  <aava-slider [multiRange]="true" [min]="0" [max]="100" [minValue]="multiMinValue()"
                    [maxValue]="multiMaxValue()" (minValueChange)="onMultiMinValueChange($event)"
                    (maxValueChange)="onMultiMaxValueChange($event)"></aava-slider>
                  <div class="slider-values">
                    <span>Min: {{ multiMinValue() }}</span>
                    <span>Max: {{ multiMaxValue() }}</span>
                  </div>
                </div>
              </div>
            </ng-container>

            <!-- Slider with Handle Icon -->
            <ng-container *ngSwitchCase="'Slider with Handle Icon'">
              <div class="row g-3">
                <div class="col-12">
                  <aava-slider [value]="handleIconSliderValue()" [min]="0" [max]="100" [handleIcon]="'user'"
                    (valueChange)="onHandleIconSliderValueChange($event)"></aava-slider>
                  <div class="slider-values">
                    <span>Value: {{ handleIconSliderValue() }}</span>
                  </div>
                </div>
              </div>
            </ng-container>
          </ng-container>
        </div>

        <div class="code-block" *ngIf="section.showCode">
          <div class="code-content">
            <pre><code [innerText]="getExampleCode(section.title.toLowerCase())"></code></pre>
          </div>
          <button class="copy-button" (click)="copyCode(section.title.toLowerCase())">
            <!-- Icon or text for copy button -->
          </button>
        </div>
      </div>
    </section>
  </div>

  <!-- API Reference -->
  <div class="row">
    <div class="col-12">
      <section class="doc-section api-reference">
        <h2>API Reference</h2>
        <table class="api-table">
          <thead>
            <tr>
              <th>Property</th>
              <th>Type</th>
              <th>Default</th>
              <th>Description</th>
            </tr>
          </thead>
          <tbody>
            <tr *ngFor="let prop of apiProps">
              <td>
                <code>{{ prop.name }}</code>
              </td>
              <td>
                <code>{{ prop.type }}</code>
              </td>
              <td>
                <code>{{ prop.default }}</code>
              </td>
              <td>{{ prop.description }}</td>
            </tr>
          </tbody>
        </table>
      </section>
    </div>
  </div>

  <!-- Events -->
  <div class="row">
    <div class="col-12">
      <section class="doc-section">
        <h2>Events</h2>
        <table class="api-table">
          <thead>
            <tr>
              <th>Event</th>
              <th>Type</th>
              <th>Description</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td><code>valueChange</code></td>
              <td><code>EventEmitter&lt;number&gt;</code></td>
              <td>Emitted when the slider value changes</td>
            </tr>
            <tr>
              <td><code>dragStart</code></td>
              <td><code>EventEmitter&lt;void&gt;</code></td>
              <td>Emitted when user starts dragging the slider</td>
            </tr>
            <tr>
              <td><code>dragEnd</code></td>
              <td><code>EventEmitter&lt;void&gt;</code></td>
              <td>Emitted when user stops dragging the slider</td>
            </tr>
          </tbody>
        </table>
      </section>
    </div>
  </div>
</div>