import { Component, signal } from '@angular/core';
import { CommonModule } from '@angular/common';
import { AavaSliderComponent } from '../../../../../../../play-comp-library/src/public-api';
import { FormsModule } from '@angular/forms';

@Component({
  selector: 'ava-slider-sizes-demo',
  standalone: true,
  imports: [CommonModule, AavaSliderComponent, FormsModule],
  templateUrl: './slider-sizes-demo.component.html',
  styleUrls: ['./slider-sizes-demo.component.scss'],
})
export class SliderSizesDemoComponent {
  smallValue = signal(30);
  mediumValue = signal(50);
  largeValue = signal(70);

  onSmallChange(value: number) {
    this.smallValue.set(value);
    console.log('Small slider value:', value);
  }

  onMediumChange(value: number) {
    this.mediumValue.set(value);
    console.log('Medium slider value:', value);
  }

  onLargeChange(value: number) {
    this.largeValue.set(value);
    console.log('Large slider value:', value);
  }
}
