<div class="center-demo">
  <div class="demo-section">
    <!-- <div class="demo-item">
      <h4>Default Type vs Input Type Comparison</h4>
      <div class="variant-comparison">
        <div class="variant-item">
          <h5>Default Type (with tooltip)</h5>
          <p>Current value: {{ defaultValue() }}</p>
          <ava-slider
            [value]="defaultValue()"
            [min]="0"
            [max]="100"
            [step]="1"
            type="default"
            size="medium"
            [showTooltip]="true"
            (valueChange)="onDefaultChange($event)"
          >
          </ava-slider>
          <div class="variant-info">
            <span class="variant-badge default">Default</span>
            <span class="variant-description">Shows tooltip below handle</span>
          </div>
        </div>

        <div class="variant-item">
          <h5>Input Type (textbox beside)</h5>
          <p>Current value: {{ inputVariantValue() }}</p>
        
          <div class="variant-info">
            <span class="variant-badge input">Input</span>
            <span class="variant-description">Enhanced input capabilities</span>
          </div>
        </div>
      </div>
    </div>

    <div class="demo-item">
      <h4>Input Type with Different Sizes</h4>
      <div class="size-examples">
        <div class="size-item">
          <h5>Small + Input Type</h5>
          <p>Current value: {{ inputVariantValue() }}</p>
          <ava-slider
            [value]="inputVariantValue()"
            [min]="0"
            [max]="100"
            [step]="1"
            type="input"
            size="small"
            (valueChange)="onInputVariantChange($event)"
          >
          </ava-slider>
          <div class="size-info">
            <span class="size-badge small">Small</span>
            <span class="size-description">Compact input slider</span>
          </div>
        </div>

        <div class="size-item">
          <h5>Medium + Input Type</h5>
          <p>Current value: {{ inputVariantValue() }}</p>
          <ava-slider
            [value]="inputVariantValue()"
            [min]="0"
            [max]="100"
            [step]="1"
            type="input"
            size="medium"
            (valueChange)="onInputVariantChange($event)"
          >
          </ava-slider>
          <div class="size-info">
            <span class="size-badge medium">Medium</span>
            <span class="size-description">Standard input slider</span>
          </div>
        </div>
      </div>
    </div>

    <div class="demo-item">
      <h4>Input Type with Different Ranges</h4>
      <div class="range-examples">
        <div class="range-item">
          <h5>Temperature Control</h5>
          <p>Current temperature: {{ temperatureValue() }}°C</p>
          <ava-slider
            [value]="temperatureValue()"
            [min]="16"
            [max]="30"
            [step]="1"
            type="input"
            size="medium"
            (valueChange)="onTemperatureChange($event)"
          >
          </ava-slider>
          <div class="range-info">
            <span class="range-label">Range: 16°C - 30°C</span>
            <span class="range-step">Step: 1°C</span>
          </div>
        </div>

        <div class="range-item">
          <h5>Volume Control</h5>
          <p>Current volume: {{ volumeValue() }}%</p>
          <ava-slider
            [value]="volumeValue()"
            [min]="0"
            [max]="100"
            [step]="5"
            type="input"
            size="medium"
            (valueChange)="onVolumeChange($event)"
          >
          </ava-slider>
          <div class="range-info">
            <span class="range-label">Range: 0% - 100%</span>
            <span class="range-step">Step: 5%</span>
          </div>
        </div>

        <div class="range-item">
          <h5>Brightness Control</h5>
          <p>Current brightness: {{ brightnessValue() }}%</p>
          <ava-slider
            [value]="brightnessValue()"
            [min]="0"
            [max]="100"
            [step]="10"
            type="input"
            size="medium"
            (valueChange)="onBrightnessChange($event)"
          >
          </ava-slider>
          <div class="range-info">
            <span class="range-label">Range: 0% - 100%</span>
            <span class="range-step">Step: 10%</span>
          </div>
        </div>
      </div>
    </div>

    <div class="demo-item">
      <h4>Input Type with Precision</h4>
      <div class="precision-examples">
        <div class="precision-item">
          <h5>Decimal Precision (0.1 step)</h5>
          <p>Current value: {{ precisionValue() }}</p>
          <ava-slider
            [value]="precisionValue()"
            [min]="0"
            [max]="1"
            [step]="0.1"
            type="input"
            size="medium"
            (valueChange)="onPrecisionChange($event)"
          >
          </ava-slider>
          <div class="precision-info">
            <span class="precision-label">Range: 0.0 - 1.0</span>
            <span class="precision-step">Step: 0.1</span>
          </div>
        </div>

        <div class="precision-item">
          <h5>Custom Step (25 step)</h5>
          <p>Current value: {{ customStepValue() }}</p>
          <ava-slider
            [value]="customStepValue()"
            [min]="0"
            [max]="100"
            [step]="25"
            type="input"
            size="medium"
            (valueChange)="onCustomStepChange($event)"
          >
          </ava-slider>
          <div class="precision-info">
            <span class="precision-label">Range: 0 - 100</span>
            <span class="precision-step">Step: 25</span>
          </div>
        </div>
      </div>
    </div>

    <div class="demo-item">
      <h4>Input Variant Features</h4>
      <div class="features-list">
        <div class="feature-item">
          <span class="feature-icon">✓</span>
          <span class="feature-text">Enhanced keyboard navigation</span>
        </div>
        <div class="feature-item">
          <span class="feature-icon">✓</span>
          <span class="feature-text">Better accessibility support</span>
        </div>
        <div class="feature-item">
          <span class="feature-icon">✓</span>
          <span class="feature-text">Improved input validation</span>
        </div>
        <div class="feature-item">
          <span class="feature-icon">✓</span>
          <span class="feature-text">Enhanced user interaction</span>
        </div>
      </div>
    </div> -->
    <aava-slider [value]="inputVariantValue()" [min]="0" [max]="100" [step]="1" type="input" size="medium"
      (valueChange)="onInputVariantChange($event)">
    </aava-slider>
  </div>
</div>