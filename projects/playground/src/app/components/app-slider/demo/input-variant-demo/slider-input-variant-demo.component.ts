import { Component, signal } from '@angular/core';
import { CommonModule } from '@angular/common';
import { AavaSliderComponent } from '../../../../../../../play-comp-library/src/public-api';
import { FormsModule } from '@angular/forms';

@Component({
  selector: 'ava-slider-input-variant-demo',
  standalone: true,
  imports: [CommonModule, AavaSliderComponent, FormsModule],
  templateUrl: './slider-input-variant-demo.component.html',
  styleUrls: ['./slider-input-variant-demo.component.scss'],
})
export class SliderInputVariantDemoComponent {
  // Default slider values
  defaultValue = signal(50);
  inputVariantValue = signal(75);

  // Input variant with different ranges
  temperatureValue = signal(22);
  volumeValue = signal(80);
  brightnessValue = signal(60);

  // Input variant with decimal steps
  precisionValue = signal(0.5);

  // Input variant with custom step
  customStepValue = signal(25);

  onDefaultChange(value: number) {
    this.defaultValue.set(value);
    console.log('Default slider value:', value);
  }

  onInputVariantChange(value: number) {
    this.inputVariantValue.set(value);
    console.log('Input variant slider value:', value);
  }

  onTemperatureChange(value: number) {
    this.temperatureValue.set(value);
    console.log('Temperature changed:', value + '°C');
  }

  onVolumeChange(value: number) {
    this.volumeValue.set(value);
    console.log('Volume changed:', value + '%');
  }

  onBrightnessChange(value: number) {
    this.brightnessValue.set(value);
    console.log('Brightness changed:', value + '%');
  }

  onPrecisionChange(value: number) {
    this.precisionValue.set(value);
    console.log('Precision value changed:', value);
  }

  onCustomStepChange(value: number) {
    this.customStepValue.set(value);
    console.log('Custom step value changed:', value);
  }
}
