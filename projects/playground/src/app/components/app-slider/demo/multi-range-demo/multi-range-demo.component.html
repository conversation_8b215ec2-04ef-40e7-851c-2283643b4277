<div class="demo-container">
  <div class="slider-group">
    <aava-slider [multiRange]="true" [min]="0" [max]="100" [minValue]="minValue1" [maxValue]="maxValue1"
      (minValueChange)="onMinValue1Change($event)" (maxValueChange)="onMaxValue1Change($event)"></aava-slider>
    <br />
    <br />

    <p class="range-value">Range: {{ minValue1 }} - {{ maxValue1 }}</p>
  </div>

  <!-- <div class="slider-group">
    <h3>Price Range Slider</h3>
    <ava-slider
      [multiRange]="true"
      [min]="0"
      [max]="1000"
      [step]="10"
      [minValue]="minValue2"
      [maxValue]="maxValue2"
      (minValueChange)="onMinValue2Change($event)"
      (maxValueChange)="onMaxValue2Change($event)"
    ></ava-slider>
    <p>Price: ${{ minValue2 }} - ${{ maxValue2 }}</p>
  </div>

  <div class="slider-group">
    <h3>Custom Step Multi-Range</h3>
    <ava-slider
      [multiRange]="true"
      [min]="0"
      [max]="50"
      [step]="5"
      [minValue]="minValue3"
      [maxValue]="maxValue3"
      (minValueChange)="onMinValue3Change($event)"
      (maxValueChange)="onMaxValue3Change($event)"
    ></ava-slider>
    <p>Values: {{ minValue3 }} to {{ maxValue3 }} (step: 5)</p>
  </div> -->
</div>