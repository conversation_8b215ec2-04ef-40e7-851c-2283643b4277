import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { AavaSliderComponent } from '../../../../../../../play-comp-library/src/public-api';

@Component({
  selector: 'ava-multi-range-demo',
  standalone: true,
  imports: [CommonModule, AavaSliderComponent],
  templateUrl: './multi-range-demo.component.html',
  styleUrls: ['./multi-range-demo.component.scss'],
})
export class MultiRangeDemoComponent {
  // Basic multi-range
  minValue1 = 20;
  maxValue1 = 80;

  // Price range
  minValue2 = 100;
  maxValue2 = 500;

  // Custom step
  minValue3 = 10;
  maxValue3 = 40;

  onMinValue1Change(value: number) {
    this.minValue1 = value;
  }

  onMaxValue1Change(value: number) {
    this.maxValue1 = value;
  }

  onMinValue2Change(value: number) {
    this.minValue2 = value;
  }

  onMaxValue2Change(value: number) {
    this.maxValue2 = value;
  }

  onMinValue3Change(value: number) {
    this.minValue3 = value;
  }

  onMaxValue3Change(value: number) {
    this.maxValue3 = value;
  }
}
