<div class="demo-container">
  <h2>Icon Thumb Slider Demo</h2>

  <div class="slider-group">
    <h3>Volume Control</h3>
    <aava-slider [min]="0" [max]="100" [value]="volumeValue" [handleIcon]="'volume-2'" [iconStart]="'volume-x'"
      [iconEnd]="'volume-2'" (valueChange)="onVolumeChange($event)"></aava-slider>
    <p>Volume: {{ volumeValue }}%</p>
  </div>

  <div class="slider-group">
    <h3>Brightness Control</h3>
    <aava-slider [min]="0" [max]="100" [value]="brightnessValue" [handleIcon]="'sun'" [iconStart]="'moon'"
      [iconEnd]="'sun'" (valueChange)="onBrightnessChange($event)"></aava-slider>
    <p>Brightness: {{ brightnessValue }}%</p>
  </div>

  <div class="slider-group">
    <h3>Price Range</h3>
    <aava-slider [min]="0" [max]="1000" [step]="10" [value]="priceValue" [handleIcon]="'dollar-sign'"
      (valueChange)="onPriceChange($event)"></aava-slider>
    <p>Price: ${{ priceValue }}</p>
  </div>

  <div class="slider-group">
    <h3>Temperature Control</h3>
    <aava-slider [min]="-10" [max]="50" [value]="tempValue" [handleIcon]="'thermometer'" [iconStart]="'snowflake'"
      [iconEnd]="'flame'" (valueChange)="onTempChange($event)"></aava-slider>
    <p>Temperature: {{ tempValue }}°C</p>
  </div>
</div>