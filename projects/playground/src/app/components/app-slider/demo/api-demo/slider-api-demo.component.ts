import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { AavaSliderComponent } from '../../../../../../../play-comp-library/src/public-api';
import { FormsModule } from '@angular/forms';

@Component({
  selector: 'ava-slider-api-demo',
  standalone: true,
  imports: [CommonModule, AavaSliderComponent, FormsModule],
  templateUrl: './slider-api-demo.component.html',
  styleUrls: ['./slider-api-demo.component.scss'],
})
export class SliderApiDemoComponent {
  // Basic properties
  value = 50;
  min = 0;
  max = 100;
  step = 5;
  customValue = 75;
  tooltipValue = 60;
  noTooltipValue = 40;
  eventValue = 30;

  // Events
  onValueChange(value: number) {
    console.log('Value changed:', value);
  }

  onCustomChange(value: number) {
    console.log('Custom value changed:', value);
  }
}
