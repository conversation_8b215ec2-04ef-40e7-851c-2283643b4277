import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { AavaSliderComponent } from '../../../../../../../play-comp-library/src/public-api';
import { FormsModule } from '@angular/forms';

@Component({
  selector: 'ava-slider-basic-demo',
  standalone: true,
  imports: [CommonModule, AavaSliderComponent, FormsModule],
  templateUrl: './slider-basic-demo.component.html',
  styleUrls: ['./slider-basic-demo.component.scss'],
})
export class SliderBasicDemoComponent {
  singleValue = 50;
  customValue = 75;
  tooltipValue = 60;
  noTooltipValue = 40;

  onSingleChange(value: number) {
    console.log('Single slider value:', value);
  }

  onCustomChange(value: number) {
    console.log('Custom slider value:', value);
  }

  onTooltipChange(value: number) {
    console.log('Tooltip slider value:', value);
  }

  onNoTooltipChange(value: number) {
    console.log('No tooltip slider value:', value);
  }
}
