<div class="center-demo">
  <div class="demo-section">
    <h3>Slider Examples</h3>

    <div class="demo-item">
      <h4>Basic Slider</h4>
      <p>Current value: {{ enabledValue }}</p>
      <aava-slider [value]="enabledValue" [min]="0" [max]="100" [step]="5" (valueChange)="onEnabledChange($event)">
      </aava-slider>
    </div>

    <div class="demo-item">
      <h4>Custom Range Slider</h4>
      <p>Current value: {{ disabledValue }}</p>
      <aava-slider [value]="disabledValue" [min]="0" [max]="200" [step]="10" (valueChange)="onDisabledChange($event)">
      </aava-slider>
    </div>

    <div class="demo-item">
      <h4>Decimal Step Slider</h4>
      <p>Current value: {{ readonlyValue }}</p>
      <aava-slider [value]="readonlyValue" [min]="0" [max]="1" [step]="0.1" (valueChange)="onReadonlyChange($event)">
      </aava-slider>
    </div>

    <div class="demo-item">
      <h4>Comparison Examples</h4>
      <div class="state-comparison">
        <div class="state-item">
          <span class="state-label">Basic</span>
          <aava-slider [value]="60" [min]="0" [max]="100"> </aava-slider>
        </div>
        <div class="state-item">
          <span class="state-label">Custom Range</span>
          <aava-slider [value]="60" [min]="0" [max]="200"> </aava-slider>
        </div>
        <div class="state-item">
          <span class="state-label">Decimal</span>
          <aava-slider [value]="0.6" [min]="0" [max]="1" [step]="0.1">
          </aava-slider>
        </div>
      </div>
    </div>
  </div>
</div>