<div class="t-container">
    <div class="checkbox-grid">
        <ava-checkbox variant="default" size="sm"></ava-checkbox>
        <ava-checkbox variant="default" size="md"></ava-checkbox>
        <ava-checkbox variant="default" size="lg"></ava-checkbox>

        <ava-checkbox variant="default" size="sm" [isChecked]="true"></ava-checkbox>
        <ava-checkbox variant="default" size="md" [isChecked]="true"></ava-checkbox>
        <ava-checkbox variant="default" size="lg" [isChecked]="true"></ava-checkbox>

        <ava-checkbox variant="default" size="sm" [indeterminate]="true"></ava-checkbox>
        <ava-checkbox variant="default" size="md" [indeterminate]="true"></ava-checkbox>
        <ava-checkbox variant="default" size="lg" [indeterminate]="true"></ava-checkbox>

        <ava-checkbox variant="with-bg" size="sm"></ava-checkbox>
        <ava-checkbox variant="with-bg" size="md"></ava-checkbox>
        <ava-checkbox variant="with-bg" size="lg"></ava-checkbox>

        <ava-checkbox variant="with-bg" size="sm" [isChecked]="true"></ava-checkbox>
        <ava-checkbox variant="with-bg" size="md" [isChecked]="true"></ava-checkbox>
        <ava-checkbox variant="with-bg" size="lg" [isChecked]="true"></ava-checkbox>
        <ava-checkbox variant="animated" size="sm"></ava-checkbox>
        <ava-checkbox variant="animated" size="md"></ava-checkbox>
        <ava-checkbox variant="animated" size="lg"></ava-checkbox>
        <ava-checkbox variant="animated" size="sm" [isChecked]="true"></ava-checkbox>
        <ava-checkbox variant="animated" size="md" [isChecked]="true"></ava-checkbox>
        <ava-checkbox variant="animated" size="lg" [isChecked]="true"></ava-checkbox>
    </div>

    <div class="checkbox-list">
        <ava-checkbox variant="default" size="md" label="Label"></ava-checkbox>
        <ava-checkbox variant="default" size="md" [disable]="true"></ava-checkbox>
        <ava-checkbox variant="default" size="md" [disable]="true" [isChecked]="true"></ava-checkbox>
        <ava-checkbox variant="default" size="md" [disable]="true" [indeterminate]="true"></ava-checkbox>
    </div>
</div>
