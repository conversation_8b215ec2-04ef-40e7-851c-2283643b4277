<div class="t-container">
    <div class="each">
        <div>
            <p>Large</p>
            <ava-select size="lg" [multiple]="true" label="Select Users" placeholder="Select users">
                <ava-select-option *ngFor="let user of users" [value]="user.value">
                    <ava-checkbox size="lg" variant="with-bg"
                        [isChecked]="selectedUsers.includes(user.value)"></ava-checkbox>
                    {{ user.label }}
                </ava-select-option>
            </ava-select>

            <ava-select size="lg" label="Select User" placeholder="Select a user">
                <ava-select-option *ngFor="let user of users" [value]="user.value">
                    <div class="option-row">
                        <div class="option-left">
                            <ava-avatars size="large" shape="pill" [imageUrl]="sampleImageUrl"
                                altText="User avatar"></ava-avatars>
                            {{ user.label }}
                        </div>
                        <ava-icon iconSize="25" iconName="user"></ava-icon>
                    </div>
                </ava-select-option>
            </ava-select>

            <ava-select size="lg" label="Select User" placeholder="Select a user">
                <ava-select-option *ngFor="let user of users" [value]="user.value">
                    <div class="option-row">
                        <div class="option-left">
                            <ava-checkbox size="lg" variant="with-bg"
                                [isChecked]="selectedUsers.includes(user.value)"></ava-checkbox>
                            <ava-avatars size="large" shape="pill" [imageUrl]="sampleImageUrl"
                                altText="User avatar"></ava-avatars>
                            <ava-icon iconSize="25" iconName="user"></ava-icon>
                            {{ user.label }}
                        </div>
                        <ava-icon iconSize="25" iconName="user"></ava-icon>
                    </div>
                </ava-select-option>
            </ava-select>

            <ava-select size="lg" label="Select User" placeholder="Select a user">
                <ava-select-option *ngFor="let user of users" [value]="user.value">
                    <div class="option-row">
                        <div class="option-left">
                            {{ user.label }}
                        </div>
                        <ava-icon iconSize="20" iconName="user"></ava-icon>
                    </div>
                </ava-select-option>
            </ava-select>


            <ava-select size="lg" label="Select User" placeholder="Select a user">
                <ava-select-option *ngFor="let user of users" [value]="user.value">
                    {{ user.label }}
                </ava-select-option>
            </ava-select>

            <ava-select size="lg" label="Select User" placeholder="Select a user">
                <ava-select-option *ngFor="let user of users" [value]="user.value">
                    <div class="option-row">
                        <div class="option-left">
                            {{ user.label }}
                        </div>
                        <ava-icon iconSize="20" iconName="user"></ava-icon>
                    </div>
                </ava-select-option>
            </ava-select>

        </div>

    </div>

    <div class="each error-row">
        <div class="b">
            <p>Small</p>
            <form [formGroup]="form">
                <ava-select size="sm" label="Select Status" placeholder="Select a status" [required]="true"
                    [error]="getFieldError('status')" formControlName="status">
                    <ava-select-option *ngFor="let status of statusOptions" [value]="status.value">
                        <div style="display: flex; align-items: center; gap: 8px;">
                            <ava-icon iconSize="15" [iconName]="status.icon" [iconColor]="status.color"></ava-icon>
                            {{ status.label }}
                        </div>
                    </ava-select-option>
                </ava-select>
            </form>
            <div class="mt-3">
                <ava-button variant="secondary" label="Trigger Error" (click)="triggerError()"></ava-button>
                <ava-button variant="success" label="Clear Error" (click)="clearError()"></ava-button>
            </div>
        </div>

        <div>
            <p>Medium</p>
            <form [formGroup]="form">
                <ava-select size="md" label="Select Status" placeholder="Select a status" [required]="true"
                    [error]="getFieldError('status')" formControlName="status">
                    <ava-select-option *ngFor="let status of statusOptions" [value]="status.value">
                        <div style="display: flex; align-items: center; gap: 8px;">
                            <ava-icon iconSize="15" [iconName]="status.icon" [iconColor]="status.color"></ava-icon>
                            {{ status.label }}
                        </div>
                    </ava-select-option>
                </ava-select>
            </form>
            <div class="mt-3">
                <ava-button variant="secondary" label="Trigger Error" (click)="triggerError()"></ava-button>
                <ava-button variant="success" label="Clear Error" (click)="clearError()"></ava-button>
            </div>
        </div>

        <div>
            <p>Large</p>
            <form [formGroup]="form">
                <ava-select size="lg" label="Select Status" placeholder="Select a status" [required]="true"
                    [error]="getFieldError('status')" formControlName="status">
                    <ava-select-option *ngFor="let status of statusOptions" [value]="status.value">
                        <div style="display: flex; align-items: center; gap: 8px;">
                            <ava-icon iconSize="15" [iconName]="status.icon" [iconColor]="status.color"></ava-icon>
                            {{ status.label }}
                        </div>
                    </ava-select-option>
                </ava-select>
            </form>
            <div class="mt-3">
                <ava-button variant="secondary" label="Trigger Error" (click)="triggerError()"></ava-button>
                <ava-button variant="success" label="Clear Error" (click)="clearError()"></ava-button>
            </div>
        </div>
    </div>
</div>