.ava-spinner-container {
  display: flex;
  flex-direction: row; // horizontal alignment
  justify-content: center;
  align-items: center;
  gap: 1.5rem; // space between spinners
  padding: var(--spinner-padding);


  .spinner {
    border-radius: var(--spinner-border-radius);
    transition: transform 0.45s linear;

    &.progress-spinner {
      animation: none !important;
    }

    /* Dynamic Rotation States */
    &.rotate-25 {
      transform: rotate(90deg);
    }

    &.rotate-50 {
      transform: rotate(180deg);
    }

    &.rotate-75 {
      transform: rotate(270deg);
    }

    &.rotate-100 {
      transform: rotate(360deg);
    }


    /* Named color variants */
    &.primary {
      border: 3px solid var(--spinner-primary-track);
      border-top-color: var(--spinner-primary-fill);
    }

    &.secondary {
      border: 3px solid var(--spinner-secondary-track);
      border-top-color: var(--spinner-secondary-fill);
    }

    &.success {
      border: 3px solid var(--spinner-success-track);
      border-top-color: var(--spinner-success-fill);
    }

    &.warning {
      border: 3px solid var(--spinner-warning-track);
      border-top-color: var(--spinner-warning-fill);

    }

    &.danger {
      border: 3px solid var(--spinner-error-track);
      border-top-color: var(--spinner-error-fill);

    }

    &.animated {
      animation: spin 3s linear infinite;
    }

    &.circular {
      border-width: 5px;
    }

    &.dotted {
      position: relative;
      border: 3px dotted var(--spinner-primary-track);
      animation: none !important;

      &::before {
        content: '';
        position: absolute;
        top: -3px;
        left: -3px;
        right: -3px;
        bottom: -3px;
        border: 3px dotted transparent;
        border-top-color: var(--spinner-primary-fill);
        border-radius: inherit;
        animation: spin 3s linear infinite;
      }

      &.animated::before {
        animation: spin 3s linear infinite;
      }

      &:not(.animated)::before {
        animation: none;
      }

      /* Color variants for dotted */
      &.primary {
        border-color: var(--spinner-primary-track);

        &::before {
          border-top-color: var(--spinner-primary-fill);
        }
      }

      &.secondary {
        border-color: var(--spinner-secondary-track);

        &::before {
          border-top-color: var(--spinner-secondary-fill);
        }
      }

      &.success {
        border-color: var(--spinner-success-track);

        &::before {
          border-top-color: var(--spinner-success-fill);
        }
      }

      &.warning {
        border-color: var(--spinner-warning-track);

        &::before {
          border-top-color: var(--spinner-warning-fill);
        }
      }

      &.danger {
        border-color: var(--spinner-error-track);

        &::before {
          border-top-color: var(--spinner-error-fill);
        }
      }
    }

    &.partial {
      border: 3px solid transparent;
      border-top-color: var(--spinner-primary-fill);
    }

    &.gradient {
      border: 3px solid transparent;
      border-radius: var(--spinner-border-radius);
      background: conic-gradient(from 0deg,
          var(--spinner-primary-fill),
          transparent 70%,
          var(--spinner-primary-fill)) border-box;
      mask: var(--spinner-mask-layer);
      mask-composite: xor;
      -webkit-mask: var(--spinner-mask-layer);
      -webkit-mask-composite: xor;


      &.primary {
        background: conic-gradient(from 0deg,
            var(--spinner-primary-fill),
            transparent 70%,
            var(--spinner-primary-fill)) border-box;
      }

      &.secondary {
        background: conic-gradient(from 0deg,
            var(--spinner-secondary-fill),
            transparent 70%,
            var(--spinner-secondary-fill)) border-box;
      }

      &.success {
        background: conic-gradient(from 0deg,
            var(--spinner-success-fill),
            transparent 70%,
            var(--spinner-success-fill)) border-box;
      }

      &.warning {
        background: conic-gradient(from 0deg,
            var(--spinner-warning-fill),
            transparent 70%,
            var(--spinner-warning-fill)) border-box;
      }

      &.danger {
        background: conic-gradient(from 0deg,
            var(--spinner-error-fill),
            transparent 70%,
            var(--spinner-error-fill)) border-box;
      }
    }

    &.dashed {
      position: relative;
      border: 3px dashed var(--spinner-primary-track);
      animation: none !important;

      &::before {
        content: '';
        position: absolute;
        top: -3px;
        left: -3px;
        right: -3px;
        bottom: -3px;
        border: 3px dashed transparent;
        border-top-color: var(--spinner-primary-fill);
        border-radius: inherit;
        animation: spin 3s linear infinite;
      }

      &.animated::before {
        animation: spin 3s linear infinite;
      }

      &:not(.animated)::before {
        animation: none;
      }

      /* Color variants for dashed */
      &.primary {
        border-color: var(--spinner-primary-track);

        &::before {
          border-top-color: var(--spinner-primary-fill);
        }
      }

      &.secondary {
        border-color: var(--spinner-secondary-track);

        &::before {
          border-top-color: var(--spinner-secondary-fill);
        }
      }

      &.success {
        border-color: var(--spinner-success-track);

        &::before {
          border-top-color: var(--spinner-success-fill);
        }
      }

      &.warning {
        border-color: var(--spinner-warning-track);

        &::before {
          border-top-color: var(--spinner-warning-fill);
        }
      }

      &.danger {
        border-color: var(--spinner-error-track);

        &::before {
          border-top-color: var(--spinner-error-fill);
        }
      }
    }

    &.size-sm {
      width: var(--spinner-size-sm);
      height: var(--spinner-size-sm);
    }

    &.size-md {
      width: var(--spinner-size-md);
      height: var(--spinner-size-md);
    }

    &.size-lg {
      width: var(--spinner-size-lg);
      height: var(--spinner-size-lg);
    }

    &.size-xl {
      width: var(--spinner-size-xl);
      height: var(--spinner-size-xl);

    }
  }
}

:host.spin {
  display: inline-block;
  animation: spin 1.2s linear infinite;
  transform-origin: center;
}



/* Animation */
@keyframes spin {
  to {
    transform: rotate(360deg);
  }


}