import { CommonModule } from '@angular/common';
import { Component, Input, HostBinding } from '@angular/core';
export type SpinnerSize = 'xs' | 'sm' | 'md' | 'lg' | 'xl';
export type SpinnerType = 'circular' | 'dotted' | 'partial' | 'gradient' | 'dashed';
@Component({
  selector: 'aava-spinner',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './aava-spinner.component.html',
  styleUrls: ['./aava-spinner.component.scss']
})
export class AavaSpinnerComponent {
  @Input() color: 'primary' | 'secondary' | 'success' | 'warning' | 'danger' | '#1681FF' = 'primary';
  @Input() size: SpinnerSize = 'md';
  @Input() animation: boolean = false;
  @Input() type: SpinnerType = "circular";
  @Input() progressIndex: number = 25
  private static nextId = 0;
  public gradientId: string;


  constructor() {
    this.gradientId = `ava_spinner_gradient_${AavaSpinnerComponent.nextId++}`;
  }

  get colors(): string {
    if (this.color.startsWith('#') || this.color.startsWith('rgb')) {
      return this.color;
    }
    switch (this.color) {
      case 'primary':
        return 'var(--spinner-primary-fill)';
      case 'secondary':
        return 'var(--spinner-secondary-fill)';
      case 'success':
        return 'var(--spinner-success-fill)';
      case 'warning':
        return 'var(--spinner-warning-fill)';
      case 'danger':
        return 'var(--spinner-error-fill)';
      default:
        return 'var(--spinner-primary-fill)';
    }
  }


  private sizeMap: Record<Exclude<SpinnerSize, number>, number> = {
    xs: 11.696,
    sm: 14.62,
    md: 17.544,
    lg: 35.088,
    xl: 46.784
  };
  get sizePx(): number {
    return typeof this.size === 'number' ? this.size : this.sizeMap[this.size];
  }


  @HostBinding('class.spin')
  get spinning(): boolean {
    return this.animation;
  }
}
