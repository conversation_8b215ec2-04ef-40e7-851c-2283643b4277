/* Breadcrumb container */
.breadcrumb {
  list-style: none;
  padding: 0;
  margin: 0;
  background: var(--breadcrumbs-background);
  font: var(--breadcrumbs-font);
  display: flex;
  align-items: center;
  gap: 0;
  flex-wrap: wrap;
}

/* Breadcrumb items */
.breadcrumb li {
  display: flex;
  align-items: center;
  color: var(--breadcrumbs-item-text);
  font: var(--breadcrumbs-item-font);
}

/* Breadcrumb links */
.breadcrumb li a {
  color: var(--breadcrumbs-item-text);
  text-decoration: none;
  padding: 0;
  border-radius: var(--breadcrumbs-item-border-radius);
  transition: var(--breadcrumbs-item-transition);
}

.breadcrumb li a:hover {
  color: var(--breadcrumbs-item-hover-text);
  background: var(--breadcrumbs-item-hover-background);
}

.breadcrumb li a:focus,
.breadcrumb li a:active {
  border: none !important;
  outline: none !important;
  box-shadow: none !important;
}

/* Current breadcrumb (last item) */
.breadcrumb li > span {
  color: var(--breadcrumbs-item-current-text);
  font-weight: var(--breadcrumbs-item-current-font-weight);
  background: var(--breadcrumbs-item-current-background);
  padding: 0;
}

/* Breadcrumb item container (for icon + text) */
.breadcrumb-item {
  display: inline-flex;
  align-items: center;
  gap: 4px; /* Small gap between icon and text */
}

.breadcrumb li span ava-icon lucide-icon svg {
  color: var(--breadcrumbs-item-current-text) !important;
  // fill: var(--breadcrumbs-item-current-text) !important;
  stroke: var(--breadcrumbs-item-current-text) !important;
}


/* Removed clicked state styling to prevent gray background */

.breadcrumb-item ava-icon {
  flex-shrink: 0; /* Prevent icon from shrinking */
}

/* Additional hover selectors for better coverage */
.breadcrumb li:hover ava-icon lucide-icon svg {
  color: var(--breadcrumbs-item-hover-text) !important;
  stroke: var(--breadcrumbs-item-hover-text) !important;
}

/* Ensure icons don't block hover events */
.breadcrumb li ava-icon {
  pointer-events: none;
}

.breadcrumb li ava-icon .ava-icon-container {
  pointer-events: none !important;
}

.breadcrumb-item span {
  color: inherit; /* Inherit color from parent */
  font-weight: inherit; /* Inherit font-weight from parent */
}

/* Size variants */
.breadcrumb.sm {
  font: var(--breadcrumbs-size-sm-font);
}

.breadcrumb.md {
  font: var(--breadcrumbs-size-md-font);
}

.breadcrumb.lg {
  font: var(--breadcrumbs-size-lg-font);
}

/* Size classes */
.breadcrumb.sm li {
  font-size: var(--breadcrumbs-size-sm-font);
}

.breadcrumb.sm li a,
.breadcrumb.sm li span {
  padding: var(--breadcrumbs-size-sm-padding);
}

.breadcrumb.md li {
  font-size: var(--breadcrumbs-size-md-font);
}

.breadcrumb.md li a,
.breadcrumb.md li span {
  padding: var(--breadcrumbs-size-md-padding);
}

.breadcrumb.lg li {
  font-size: var(--breadcrumbs-size-lg-font);
}

.breadcrumb.lg li a,
.breadcrumb.lg li span {
  padding: var(--breadcrumbs-size-lg-padding);
}

/* Active states */
.breadcrumb li.active a {
  color: var(--breadcrumbs-item-active-text);
  background: var(--breadcrumbs-item-active-background);
}

/* Removed clicked state styling to prevent gray background */

/* Disabled state */
.breadcrumb li.disabled a {
  color: var(--breadcrumbs-item-disabled-text);
  cursor: var(--breadcrumbs-item-disabled-cursor);
  pointer-events: none;
}

/* Accessibility improvements */
.breadcrumb li a:focus {
  outline: 2px solid var(--color-brand-primary);
  outline-offset: 2px;
}

/* Remove margin from last icon */
.breadcrumb li:last-child ava-icon {
  display: none;
}

/* Ellipsis styling for collapsible breadcrumbs */
.breadcrumb-ellipsis {
  display: flex;
  align-items: center;
}

.ellipsis {
  color: var(--breadcrumbs-item-text);
  font: var(--breadcrumbs-item-font);
  padding: var(--breadcrumbs-item-padding);
  user-select: none;
}


.breadcrumb li ava-icon.breadcrumb-separator lucide-icon svg
{
  color:  var(--breadcrumbs-item-text) !important; /* Green color */
  stroke:  var(--breadcrumbs-item-text) !important; /* Green stroke for SVG icons */
}
