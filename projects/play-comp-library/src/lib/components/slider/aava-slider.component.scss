.slider-container {
  width: 100%;
  display: inline-flex;
  height: var(--slider-container-height);
  padding: var(--slider-container-padding);
  align-items: center;
  gap: var(--slider-container-gap);
  flex-shrink: 0;

  &.slider-input-type {
    display: flex;
    align-items: center;
    gap: var(--slider-input-gap);
  }
}

.slider-wrapper {
  
  flex: 1;
}

// Size variants
.slider-small {
  .slider {
    height: var(--slider-size-sm-track-height);
  }

  .slider-handle {
    top: calc((var(--slider-size-sm-track-height) - var(--slider-size-sm-thumb-size)) / 2);
    width: var(--slider-size-sm-thumb-size);
    height: var(--slider-size-sm-thumb-size);
  }

  .handle-core {
    width: var(--slider-size-sm-thumb-size);
    height: var(--slider-size-sm-thumb-size);
  }

  .handle-ring {
    top: calc((var(--slider-size-sm-thumb-size) - var(--slider-size-sm-thumb-size) - 12px) / 2);
    left: calc((var(--slider-size-sm-thumb-size) - var(--slider-size-sm-thumb-size) - 12px) / 2);
    width: calc(var(--slider-size-sm-thumb-size) + 12px);
    height: calc(var(--slider-size-sm-thumb-size) + 12px);
  }
  .slider-tooltip{
  font-size: var(--slider-label-font-size-sm);
  font-weight: var(--slider-label-weight-sm);
  }

}

.slider-medium {
  .slider {
    height: var(--slider-size-md-track-height);
  }

  .slider-handle {
    top: calc((var(--slider-size-md-track-height) - var(--slider-size-md-thumb-size)) / 2);
    width: var(--slider-size-md-thumb-size);
    height: var(--slider-size-md-thumb-size);
  }

  .handle-core {
    width: var(--slider-size-md-thumb-size);
    height: var(--slider-size-md-thumb-size);
  }

  .handle-ring {
    top: calc((var(--slider-size-md-thumb-size) - var(--slider-size-md-thumb-size) - 12px) / 2);
    left: calc((var(--slider-size-md-thumb-size) - var(--slider-size-md-thumb-size) - 12px) / 2);
    width: calc(var(--slider-size-md-thumb-size) + 12px);
    height: calc(var(--slider-size-md-thumb-size) + 12px);
  }
   .slider-tooltip{
  font-size: var(--slider-label-font-size-md);
  font-weight: var(--slider-label-weight-md);
  }


}

.slider {
  position: relative;
  width: 100%;
  height: var(--slider-track-height);
  background: var(--slider-track-background);
  border-radius: var(--slider-track-border-radius);
  cursor: var(--slider-cursor);
}

.slider-fill {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  background: var(--slider-progress-background);
  border-radius: var(--slider-progress-border-radius);
  // Enhanced smooth liquid-like animation
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1),
              width 0.25s cubic-bezier(0.25, 0.46, 0.45, 0.94),
              left 0.25s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  z-index: 1;
}

// Disable transitions during dragging for immediate feedback
.slider-container.dragging .slider-fill {
  transition: none;
}

.slider-container.dragging .slider-handle {
  transition: none;
}

.slider-container.dragging .handle-core {
  transition: none;
}

.slider-container.dragging .handle-ring {
  transition: none;
}

.slider-handle {
  position: absolute;
  top: calc((var(--slider-track-height) - var(--slider-thumb-size)) / 2);
  width: var(--slider-thumb-size);
  height: var(--slider-thumb-size);
  transform: translateX(-50%);
  cursor: grab;
  outline: none;
  z-index: 2;
  // Enhanced smooth liquid-like animation for handle movement
  transition: left 0.25s cubic-bezier(0.25, 0.46, 0.45, 0.94),
              transform 0.2s cubic-bezier(0.4, 0, 0.2, 1),
              box-shadow 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.slider-handle:active,
.slider-handle.dragging {
  cursor: grabbing;
}

.handle-ring {
  position: absolute;
  top: calc((var(--slider-thumb-size) - var(--slider-thumb-size) - 12px) / 2);
  left: calc((var(--slider-thumb-size) - var(--slider-thumb-size) - 12px) / 2);
  width: calc(var(--slider-thumb-size) + 12px);
  height: calc(var(--slider-thumb-size) + 12px);
  border-radius: var(--slider-thumb-border-radius);
  background: color-mix(
    in srgb,
    var(--slider-progress-background) 10%,
    transparent
  );
  border: 2px solid
    color-mix(in srgb, var(--slider-progress-background) 20%, transparent);
  opacity: 0;
  // Enhanced smooth liquid-like animation for handle ring
  transition: opacity 0.2s cubic-bezier(0.4, 0, 0.2, 1),
              transform 0.2s cubic-bezier(0.4, 0, 0.2, 1),
              background 0.15s ease-out;
}

.slider-handle.hover .handle-ring,
.slider-handle.dragging .handle-ring,
.slider-handle:focus .handle-ring {
  opacity: 1;
}

.handle-core {
  position: absolute;
  top: 4;
  left: 0;
  width: var(--slider-thumb-size);
  height: var(--slider-thumb-size);
  background: var(--slider-thumb-inner-background);
  border: var(--slider-focus-ring);
  border-radius: var(--slider-thumb-border-radius);
  box-shadow: var(--slider-thumb-shadow);
  // Enhanced smooth liquid-like animation for handle core
  transition: transform 0.2s cubic-bezier(0.4, 0, 0.2, 1),
              box-shadow 0.2s cubic-bezier(0.4, 0, 0.2, 1),
              background 0.15s ease-out,
              border-color 0.15s ease-out;
}

.slider-handle.hover .handle-core,
.slider-handle.dragging .handle-core,
.slider-handle:focus .handle-core {
  transform: scale(1.08);
  box-shadow: var(--slider-thumb-shadow-hover);
}

.slider-handle:active .handle-core,
.slider-handle.dragging .handle-core {
  box-shadow: inset 0 2px 8px 0
      color-mix(in srgb, var(--slider-thumb-inner-background) 40%, white 30%),
    var(--slider-thumb-shadow);
  background: color-mix(
    in srgb,
    var(--slider-thumb-inner-background) 90%,
    white 10%
  );
  transform: scale(0.97);
}

.slider-handle:focus {
  outline: var(--slider-focus-ring);
  outline-offset: var(--slider-focus-ring-offset);
}

.slider-handle-icon {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: var(--slider-handle-icon-width);
  height: var(--slider-handle-icon-height);
  pointer-events: none;
  z-index: 3;
}

// Movable Tooltip
.slider-tooltip {
  position: absolute;
  top: 100%;
  left: 50%;
  transform: translateX(-50%);
  margin-top: var(--slider-tooltip-margin);
  padding: var(--slider-tooltip-padding);
  background: var(--slider-thumb-inner-background);
  color: var(--slider-value-color);
  border-radius: var(--slider-tooltip-border-radius);
  box-shadow: var(--slider-thumb-shadow);
  white-space: nowrap;
  pointer-events: none;
  z-index: var(--tooltip-z-index);
  font-family: var(--slider-label-font-family);
  line-height: var(--slider-label-line-height);

  // Tooltip arrow pointing up
  &::before {
    content: "";
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    border-bottom-color: var(--slider-thumb-inner-background);
  }

  // Arrow border for shadow effect
  &::after {
    content: "";
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    border-bottom-color: var(--slider-mark-background);
    z-index: -1;
  }
}

// Input box styles for input type
.slider-input-container {
  flex-shrink: 0;
  width: 48px;
  height: 28px;
}

.slider-input {
  // Exact Figma specifications using slider design tokens
  display: flex;
  width: var(--slider-input-width);
  height: var(--slider-input-height);
  padding: var(--slider-input-padding);
  justify-content: center;
  align-items: center;
  gap: var(--slider-input-gap);
  flex-shrink: 0;
  border-radius: var(--slider-input-border-radius);
  border: var(--slider-input-border);
  background: var(--slider-input-background);

  // Font styling using slider tokens
  font-size: var(--slider-input-font-size);
  font-weight: var(--slider-input-font-weight);
  font-family: var(--slider-input-font-family);
  color: var(--slider-input-color);

  // Input specific styling
  text-align: center;
  box-sizing: border-box;
  outline: none;
  margin: 0;
  transition: var(--slider-input-transition);

  // Remove default number input styling
  -webkit-appearance: none;
  -moz-appearance: textfield;
  appearance: none;

  // Hide number input spinners
  &::-webkit-outer-spin-button,
  &::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
    display: none;
  }

  // Focus state using accessibility tokens
  &:focus {
    border-color: var(--color-brand-primary);
    box-shadow: 0 0 0 var(--accessibility-focus-ring-width)
                rgba(var(--effect-color-primary), 0.2);
    outline: none;
  }

  // Hover state
  &:hover:not(:focus) {
    border-color: var(--color-border-hover, var(--global-color-gray-400));
  }

  // Disabled state
  &:disabled {
    background: var(--color-surface-disabled);
    border-color: var(--color-border-disabled);
    color: var(--slider-value-color-disabled);
    cursor: not-allowed;
  }
}

// Size variants using slider size tokens
.slider-small .slider-input {
  font-size: var(--slider-label-font-size-sm);
  font-weight: var(--slider-label-weight-sm);
  color:var(--slider-input-color);
}

.slider-medium .slider-input {
  font-size: var(--slider-label-font-size-md);
    color:var(--slider-input-color);

  font-weight: var(--slider-label-weight-md);
}
