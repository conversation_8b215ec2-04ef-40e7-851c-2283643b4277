// Timeline component with stepper-like alignment
.timeline {
  display: flex;
  width: 100%;
 
  // Horizontal orientation (similar to stepper horizontal)
  &.horizontal {
    flex-direction: row;
    justify-content: space-between;
    position: relative;
 
    .timeline-event {
      flex: 1;
      display: flex;
      flex-direction: column;
      align-items: center;
      position: relative;
      margin-bottom: 0;
 
      .timeline-icon-wrapper {
        position: relative;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 100%;
       
        .timeline-line-wrapper {
          position: absolute;
          top: 50%;
          left: 50%;
          width: 100%;
          height: 2px;
          display: flex;
          align-items: center;
          justify-content: center;
          transform: translateX(12px) translateY(-50%);
          z-index: 1;
 
          .timeline-line {
            width: 100%;
            height: 2px;
            background:var(--timeline-line-color);
          }
        }
 
        .timeline-icon {
          position: relative;
          z-index: 2;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          overflow: hidden; 
          background-color: var(--timeline-icon-bg-color);
          border: 2px solid var( --timeline-line-color);
          width: var(--timeline-icon-size, 40px);
          height: var(--timeline-icon-size, 40px);

          .timeline-icon-image {
            border-radius: 50%;
            object-fit: cover;
            width: 100%;
            height: 100%;
          }
        }
      }
 
      .timeline-content {
        text-align: center;
        background: transparent;
        padding: 8px 0;
        margin-top: 12px;
      }
 
      .timeline-card {
        margin-top: 12px;
        width: 100%;
      }
    }
  }



   &.text-align-zig-zag{
     .timeline-line-wrapper {
          position: absolute;
          top: 50%;
          left: 50%;
          width: 100%;
          height: 2px;
          display: flex;
          align-items: center;
          justify-content: center;
          transform: translateX(12px) translateY(-50%);
          z-index: 1;
 
          .timeline-line {
            width: 100%;
            height: 2px;
            background:var(--timeline-line-color);
          }
        }
    .timeline-event {
      position: relative;
      min-height: 100px; 
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
 
      .timeline-icon-wrapper {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        z-index: 3;
 
        .timeline-icon {
          background-color: var(--timeline-icon-bg-color);
          border-radius: 50%;
          width: var(--timeline-icon-size, 8px);
          height: var(--timeline-icon-size, 8px);
          overflow: hidden; 

          .timeline-icon-image {
            border-radius: 50%;
            object-fit: cover;
            width: 100%;
            height: 100%;
          }
        }
      }
 
      // Force alternating alignment with higher specificity
      &.align-up {
        .timeline-content,
        .timeline-card {
          position: absolute ;
          bottom: calc(50% + 25px);
          left: 50% ;
          transform: translateX(-50%);
          text-align: center ;
          white-space: nowrap ;
          order: unset ;
          margin: 0 ;
 
          .timeline-text {
            font-size: 14px;
            font-weight: 500;
            color: var(--timeline-text-color);
            margin: 0;
          }
 
          .timeline-time {
            font-size: 12px;
            color:var(--timeline-text-color);
            margin: 0;
          }
        }
      }
 
      &.align-down {
        .timeline-content,
        .timeline-card {
          position: absolute ;
          top: calc(50% + 25px) ;
          left: 50% ;
          transform: translateX(-50%) ;
          text-align: center ;
          white-space: nowrap ;
          order: unset;
          margin: 0 ;
 
          .timeline-text {
            font-size: 14px;
            font-weight: 500;
            color: var(--timeline-text-color);
            margin: 0;
          }
 
          .timeline-time {
            font-size: 12px;
            color: var(--timeline-text-color);
            margin: 0;
          }
        }
      }
    }
   }
 
  // Vertical orientation (similar to stepper vertical)
  &.vertical {
    flex-direction: column;
    position: relative;
    &:before {
      content: '';
      position: absolute;
      width: 2px;
      background:var(--timeline-line-color);
      z-index: 1;
    }
 
    .timeline-event {
      display: flex;
      flex-direction: row;
      align-items: flex-start;
      gap: 16px;
      position: relative;
      margin-bottom: 24px;
 
      .timeline-icon-wrapper {
        position: relative;
        display: flex;
        flex-direction: column;
        align-items: center;
        flex-shrink: 0;
 
        .timeline-icon {
          position: relative;
          z-index: 2;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          overflow: hidden; 
          background-color: var(--timeline-icon-bg-color);
          border: 2px solid var( --timeline-line-color);
          width: var(--timeline-icon-size, 40px);
          height: var(--timeline-icon-size, 40px);

          .timeline-icon-image {
            border-radius: 50%;
            object-fit: cover;
            width: 100%;
            height: 100%;
          }
        }
      }
 
      .timeline-content {
        flex: 1;
        background: transparent;
        padding: 8px 0;
      }
 
      .timeline-card {
        flex: 1;
        margin-left: 0;
      }
    }
  }
 
  // Common timeline content styles
  .timeline-content {
    .timeline-time {
      font-weight: bold;
      margin-bottom: 4px;
      color: var(--timeline-text-color);
      font-size: 14px;
    }
 
    .timeline-text {
      margin-top: 5px;
      font-size: 16px;
      color: var(--timeline-text-color);
    }
 
    .timeline-description {
      margin-top: 4px;
      font-size: 14px;
      color: var(--timeline-text-color);
      line-height: 1.4;
    }
  }
 
}

// Card-specific styles for timeline
.timeline-card {
  width: 100%;
  max-width: none;

  // Allow the card to grow with content
  ::ng-deep ava-default-card {
    width: 100%;

    .card {
      width: 100%;
      min-height: auto;
      height: auto;
    }
  }
}


.timeline.vertical {
  &:before {
    left: 50%;
    transform: translateX(-50%);
    top: 20px;
    bottom: 20px;
  }

  // Improve card spacing and alignment with dynamic heights
  .timeline-event {
    position: relative;
    margin-bottom: 0;

  
    &:not(:last-child) {
      margin-bottom: 30px;

    
      &:has(.timeline-card) {
        margin-bottom: 50px;
      }
    }

    .timeline-card {
      min-height: auto;
      height: auto;
      max-height: none;
      &.align-left {
        margin-right:20px;
      }

      &.align-right {
        margin-left: 20px;
      }
    }
  }
}

.timeline.vertical.text-align-right:before {
  left: auto;
  right: 20px;
}
 
.timeline.horizontal {
  &.text-align-up:not(.text-align-zig-zag) .timeline-event {
    .timeline-content,
    .timeline-card {
      order: -1;
      margin-bottom: 12px;
      margin-top: 0;
    }
  }
 
  &.text-align-down:not(.text-align-zig-zag) .timeline-event {
    .timeline-content,
    .timeline-card {
      order: 1;
      margin-top: 12px;
      margin-bottom: 0;
    }
  }
 
  &.text-align-zigzag-up,
  &.text-align-zigzag-down {
    position: relative;

    &:before {
      content: '';
      position: absolute;
      top: 50%;
      left: 0;
      right: 0;
      z-index: 1;
      transform: translateY(-50%);
    }
 
    .timeline-event {
      position: relative;
      display: flex;
      flex-direction: column;
      align-items: center;
 
      .timeline-icon-wrapper {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        z-index: 3;
      }
 
      // All text content goes to top
      &.text-up {
        .timeline-content,
        .timeline-card {
          position: absolute;
          bottom: calc(50% + 30px);
          left: 50%;
          transform: translateX(-50%);
          text-align: center;
          white-space: nowrap;
          margin: 0;
        }
      }
 
      // All text content goes to bottom
      &.text-down {
        .timeline-content,
        .timeline-card {
          position: absolute;
          top: calc(50% + 30px);
          left: 50%;
          transform: translateX(-50%);
          text-align: center;
          white-space: nowrap;
          margin: 0;
        }
      }
    }
  }

}
 
// Enhanced zigzag alignment styles with dynamic content-based spacing
.timeline.vertical.text-align-zigzag-left,
.timeline.vertical.text-align-zigzag-right {
  position: relative;

  .timeline-event {
    position: relative;
    width: 100%;
    margin-bottom: 0;
    min-height: auto;
    &:not(:last-child) {
      margin-bottom: 20px;
      &:has(.timeline-card) {
        margin-bottom: 60px;
      }
      &.large-content {
        margin-bottom: 80px;
      }
    }

    .timeline-icon-wrapper {
      position: absolute;
      left: 50%;
      top: 12px; 
      transform: translateX(-50%);
      z-index: 3;

      .timeline-icon {
        background-color: var(--timeline-icon-bg-color);
        border: 2px solid var(--timeline-line-color);
        border-radius: 50%;
        width: var(--timeline-icon-size, 12px);
        height: var(--timeline-icon-size, 12px);
        display: flex;
        align-items: center;
        justify-content: center;
      }
    }

    // All text content goes to left side
    &.text-left {
      .timeline-content,
      .timeline-card {
        margin-left: auto;
        margin-right: calc(50% + 35px);
        max-width: calc(50% - 55px);
        text-align: right;
        width: auto;
        word-wrap: break-word;
        overflow-wrap: break-word;
        min-height: auto;
        height: auto;
      }
    }

    // All text content goes to right side
    &.text-right {
      .timeline-content,
      .timeline-card {
        margin-right: auto;
        margin-left: calc(50% + 35px);
        max-width: calc(50% - 55px);
        text-align: left;
        width: auto;
        word-wrap: break-word;
        overflow-wrap: break-word;
        min-height: auto;
        height: auto;
      }
    }
  }
}
 
// Enhanced zig-zag styling with dynamic content-based spacing
.timeline.vertical.text-align-zig-zag {
  position: relative;

  // Main vertical connecting line
  &:before {
    content: '';
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    width: 2px;
    background: var(--timeline-line-color);
    top: 20px;
    bottom:20px;
    z-index: 1;
  }

  .timeline-event {
    position: relative;
    width: 100%;
    margin-bottom: 0;
    min-height: auto;
    &:not(:last-child) {
      margin-bottom:  20px;
      &:has(.timeline-card) {
        margin-bottom: 60px;
      }
      &.large-content {
         margin-bottom: 80px;
      }
    }

    &.align-left {
      .timeline-content,
      .timeline-card {
        margin-left: auto;
        margin-right: calc(50% + 35px);
        max-width: calc(50% - 55px);
        text-align: right;
        width: auto;
        word-wrap: break-word;
        overflow-wrap: break-word;
        min-height: auto;
        height: auto;

        &:not(.timeline-card) {
          background: transparent;
          border: none;
          box-shadow: none;
          padding: 0;
        }

        .timeline-time {
          font-size: 12px;
          color: var(--timeline-text-color);
          margin-bottom: 4px;
          font-weight: 400;
        }

        .timeline-text {
          font-size: 16px;
          font-weight: 500;
          color: var(--timeline-text-color);
          margin: 0;
          line-height: 1.2;
        }

        .timeline-description {
          font-size: 14px;
          color: var(--timeline-text-color);
          line-height: 1.4;
          margin-top: 8px;
        }
      }
    }

    &.align-right {
      .timeline-content,
      .timeline-card {
        margin-right: auto;
        margin-left: calc(50% + 35px);
        max-width: calc(50% - 55px);
        text-align: left;
        width: auto;
        word-wrap: break-word;
        overflow-wrap: break-word;
        min-height: auto;
        height: auto;
        &:not(.timeline-card) {
          background: transparent;
          border: none;
          box-shadow: none;
          padding: 0;
        }

        .timeline-time {
          font-size: 12px;
          color: var(--timeline-text-color);
          margin-bottom: 4px;
          font-weight: 400;
        }

        .timeline-text {
          font-size: 16px;
          font-weight: 500;
          color: var(--timeline-text-color);
          margin: 0;
          line-height: 1.2;
        }

        .timeline-description {
          font-size: 14px;
          color: var(--timeline-text-color);
          line-height: 1.4;
          margin-top: 8px;
        }
      }
    }
  

    // Icon positioning for dynamic layout
    .timeline-icon-wrapper {
      position: absolute;
      left: 50%;
      top: 12px; 
      transform: translateX(-50%);
      z-index: 3;

      .timeline-icon {
        background-color: var(--timeline-icon-bg-color);
        border: 2px solid var(--timeline-line-color);
        border-radius: 50%;
        width: var(--timeline-icon-size, 12px);
        height: var(--timeline-icon-size, 12px);
        display: flex;
        align-items: center;
        justify-content: center;
      }
    }
  }
}
 
// Alternative approach: Pure CSS dynamic height solution
.timeline.vertical.text-align-zig-zag {
  &.dynamic-height {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--timeline-event-spacing, 20px);
 
    .timeline-event {
      display: grid;
      grid-template-columns: 1fr auto 1fr;
      grid-template-areas: "left-content icon right-content";
      align-items: start;
      position: relative;
      margin-bottom: 0;
 
      .timeline-icon-wrapper {
        grid-area: icon;
        position: relative;
        z-index: 3;
        justify-self: center;
      }
 
      &.align-left {
        .timeline-content,
        .timeline-card {
          grid-area: left-content;
          justify-self: end;
          text-align: right;
          max-width: calc(100% - 20px);
          margin-right: 20px;
        }
      }
 
      &.align-right {
        .timeline-content,
        .timeline-card {
          grid-area: right-content;
          justify-self: start;
          text-align: left;
          max-width: calc(100% - 20px);
          margin-left: 20px;
        }
      }
    }
  }
}
 
// Responsive design
@media (max-width: 768px) {
  .timeline.horizontal {
    flex-direction: column;
 
    .timeline-event {
      margin-bottom: 24px;
 
      .timeline-line-wrapper {
        display: none;
      }
    }
  }
 
  .timeline.vertical {
    .timeline-card {
      max-width: 100%;
    }
 
    // Simplify zig-zag on mobile
    &.text-align-zig-zag {
      &:before {
        left: 20px;
        transform: none;
      }
 
      .timeline-event {
        &.align-left,
        &.align-right {
          justify-content: flex-start;
 
          .timeline-icon-wrapper {
            position: relative;
            left: auto;
            transform: none;
          }
 
          .timeline-content,
          .timeline-card {
            margin: 0 0 0 16px;
            max-width: calc(100% - 60px);
            text-align: left;
          }
        }
      }
    }
  }
}
 
 
 