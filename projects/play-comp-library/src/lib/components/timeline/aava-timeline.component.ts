import { CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, Component, Input, SimpleChanges, TemplateRef, OnInit, AfterViewInit, ElementRef, Renderer2, ViewChild, HostBinding } from '@angular/core';
import { IconComponent } from '../icon/icon.component';
import { DefaultCardComponent } from '../card/default-card/default-card.component';
 


interface TimelineEvent {
  time: string;
  text?: string;
  iconName?: string;
  iconColor?: string;
  iconSize?: string;
  imageUrl?: string;
  imageSize?: string;
  year: string;
  title?: string;
  description?: string;
}
 

@Component({
  selector: 'aava-timeline',
    imports: [CommonModule, IconComponent, DefaultCardComponent],
  templateUrl: './aava-timeline.component.html',
  styleUrl: './aava-timeline.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class AavaTimeline implements OnInit, AfterViewInit {
  @Input() events: TimelineEvent[] = [];
  @Input() sortOrder: 'ascending' | 'descending' = 'ascending';
  @Input() orientation: 'vertical' | 'horizontal' = 'vertical';
  @Input() card: boolean = false;
  @Input() cardTemplate?: TemplateRef<any>;
  @Input() iconCircleBgColor = '#fff';
  @Input() iconCircleBorderColor = '#ccc';
  @Input() iconCircleSize = '40px';
  @Input() textAlign: 'zig-zag' | 'zigzag-left' | 'zigzag-right' | 'zigzag-up' | 'zigzag-down' = 'zigzag-left';
 

  // Host bindings for CSS custom properties
  @HostBinding('style.--timeline-icon-bg-color') get iconBgColor() { return this.iconCircleBgColor; }
  @HostBinding('style.--timeline-icon-border-color') get iconBorderColor() { return this.iconCircleBorderColor; }
  @HostBinding('style.--timeline-icon-size') get iconSize() { return this.iconCircleSize; }

  @ViewChild('timelineContainer', { static: false }) timelineContainer?: ElementRef;

  sortedEvents: TimelineEvent[] = [];
 
  constructor(
    private elementRef: ElementRef,
    private renderer: Renderer2
  ) {}
 
  ngOnInit() {
    this.sortEvents();
    this.updateCSSVariables();
  }
 
  ngAfterViewInit() {
    // Component initialization complete
  }
 
  ngOnChanges(changes: SimpleChanges) {
    if (changes['events'] || changes['sortOrder']) {
      this.sortEvents();
    }
    if (changes['iconCircleBgColor'] || changes['iconCircleBorderColor']) {
      this.updateCSSVariables();
    }
  }
 
  private updateCSSVariables() {
    const element = this.elementRef.nativeElement;
    this.renderer.setStyle(element, '--icon-circle-bg-color', this.iconCircleBgColor);
    this.renderer.setStyle(element, '--icon-circle-border-color', this.iconCircleBorderColor);
  }
 
  sortEvents() {
    this.sortedEvents = [...this.events].sort((a, b) => {
      const yearComparison = a.year.localeCompare(b.year);
      if (yearComparison !== 0) {
        return this.sortOrder === 'ascending' ? yearComparison : -yearComparison;
      }
      const timeComparison = a.time.localeCompare(b.time);
      return this.sortOrder === 'ascending' ? timeComparison : -timeComparison;
    });
  }
 
  getEventAlignmentClass(index: number): string {
    if (this.textAlign === 'zig-zag') {
      if (this.orientation === 'vertical') {
        return index % 2 === 0 ? 'align-left' : 'align-right';
      } else {
        // For horizontal zig-zag, alternate up and down
        return index % 2 === 0 ? 'align-up' : 'align-down';
      }
    }
 
    // Handle new zigzag alignment options with fixed text positioning
    if (this.textAlign === 'zigzag-left' || this.textAlign === 'zigzag-right') {
      // For vertical timeline: all text on left or right, but circles still alternate
      const circlePosition = index % 2 === 0 ? 'align-left' : 'align-right';
      const textPosition = this.textAlign === 'zigzag-left' ? 'text-left' : 'text-right';
      return `${circlePosition} ${textPosition}`;
    }
 
    if (this.textAlign === 'zigzag-up' || this.textAlign === 'zigzag-down') {
      // For horizontal timeline: all text on top or bottom, but circles still alternate
      const circlePosition = index % 2 === 0 ? 'align-up' : 'align-down';
      const textPosition = this.textAlign === 'zigzag-up' ? 'text-up' : 'text-down';
      return `${circlePosition} ${textPosition}`;
    }
 
    return `align-${this.textAlign}`;
  }
 
  getTimelineClasses(): string {
    return `timeline ${this.orientation} text-align-${this.textAlign}`;
  }
 
  onReadMore(event: TimelineEvent): void {
    // Emit event or handle read more action
    console.log('Read more clicked for:', event);
    // You can add custom logic here or emit an output event
  }
 
 
}

