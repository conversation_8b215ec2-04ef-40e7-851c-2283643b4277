
<div [ngClass]="getTimelineClasses()" #timelineContainer>
    <div *ngFor="let event of sortedEvents; let i = index"
         class="timeline-event"
         [ngClass]="getEventAlignmentClass(i)">
        <div class="timeline-icon-wrapper">
         <div class="timeline-icon">
        <ng-container>
         <img *ngIf="event.imageUrl"
         [src]="event.imageUrl"
         [alt]="event.text || 'Timeline event'"
         class="timeline-icon-image"
         [style.width]="event.imageSize || iconCircleSize"
         [style.height]="event.imageSize || iconCircleSize" />
    
          <ava-icon *ngIf="!event.imageUrl && event.iconName"
              class="timeline__icon"
              [iconName]="event.iconName"
              [iconColor]="event.iconColor || ''"
              [iconSize]="event.iconSize || ''">
          </ava-icon>
        </ng-container>
</div>
            <!-- Connecting line for horizontal timeline -->
            <div *ngIf="orientation === 'horizontal' && i < sortedEvents.length - 1"
                 class="timeline-line-wrapper">
                <div class="timeline-line"></div>
            </div>
        </div>
 
        <!-- Card View -->
        <ng-container *ngIf="card; else normalView">
            <ava-default-card class="timeline-card">
                <ng-container *ngIf="cardTemplate; else defaultCardContent">
                    <ng-container *ngTemplateOutlet="cardTemplate; context: { $implicit: event, index: i }"></ng-container>
                </ng-container> 
                <ng-template #defaultCardContent>
                    <ng-content></ng-content>
                </ng-template>
            </ava-default-card>
        </ng-container>
 
        <!-- Normal View -->
        <ng-template #normalView>
            <div class="timeline-content">
                <div class="timeline-time">
                    <span *ngIf="event.year">{{ event.year }}</span>
                    <span *ngIf="event.year && event.time"> - </span>
                    <span *ngIf="event.time">{{ event.time }}</span>
                </div>
                <div class="timeline-text">{{ event.text }}</div>
                <div *ngIf="event.description" class="timeline-description">
                    {{ event.description }}
                </div>
            </div>
        </ng-template>
    </div>
</div>
 
 
 