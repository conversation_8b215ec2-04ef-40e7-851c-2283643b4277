import { CheckboxComponent } from './../../components/checkbox/checkbox.component';
import { ChangeDetectionStrategy, Component, Input, Output, EventEmitter, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { IconComponent } from '../../components/icon/icon.component';
// import { ButtonComponent } from '../../components/button/button.component';


export interface FilterOption {
  id: string | number;
  label: string;
  value: any;
  selected?: boolean;
}

export interface FilterGroup {
  id: string;
  title: string;
  options: FilterOption[];
  multiSelect?: boolean;
}

export type FilterSize = 'sm' | 'md' | 'lg' | 'xlg';


@Component({
  selector: 'ava-filter',
  standalone: true,
  imports: [CheckboxComponent, IconComponent, CommonModule],
  templateUrl: './filter.component.html',
  styleUrl: './filter.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class FilterComponent implements OnInit {
  @Input() size: FilterSize = 'md';
  @Input() title: string = 'Filter';
  @Input() filterGroups: FilterGroup[] = [];
  @Input() showClearAll: boolean = true;
  @Input() showApplyButton: boolean = false;
  @Input() isOpen: boolean = false;
  @Input() position: 'left' | 'right' = 'left';
  @Input() maxHeight: string = '400px';
  @Input() width: string = 'auto';
  @Input() disabled: boolean = false;
  @Input() class: string = '';

  @Output() filterChange = new EventEmitter<{ [groupId: string]: FilterOption[] }>();
  @Output() clearAll = new EventEmitter<void>();
  @Output() apply = new EventEmitter<{ [groupId: string]: FilterOption[] }>();
  @Output() toggleFilter = new EventEmitter<boolean>();

  private selectedFilters: { [groupId: string]: FilterOption[] } = {};

  ngOnInit() {
    this.initializeSelectedFilters();
  }

  private initializeSelectedFilters() {
    this.filterGroups.forEach(group => {
      this.selectedFilters[group.id] = group.options.filter(option => option.selected) || [];
    });
  }

  toggleFilterPanel() {
    if (this.disabled) return;
    this.isOpen = !this.isOpen;
    this.toggleFilter.emit(this.isOpen);
  }

  onOptionChange(groupId: string, option: FilterOption, isChecked: boolean) {
    const group = this.filterGroups.find(g => g.id === groupId);
    if (!group) return;

    if (!this.selectedFilters[groupId]) {
      this.selectedFilters[groupId] = [];
    }

    if (group.multiSelect !== false) {
      // Multi-select mode (default)
      if (isChecked) {
        if (!this.selectedFilters[groupId].find(f => f.id === option.id)) {
          this.selectedFilters[groupId].push(option);
        }
      } else {
        this.selectedFilters[groupId] = this.selectedFilters[groupId].filter(f => f.id !== option.id);
      }
    } else {
      // Single-select mode
      if (isChecked) {
        this.selectedFilters[groupId] = [option];
        // Uncheck other options in the same group
        group.options.forEach(opt => {
          if (opt.id !== option.id) {
            opt.selected = false;
          }
        });
      } else {
        this.selectedFilters[groupId] = [];
      }
    }

    option.selected = isChecked;
    this.emitFilterChange();
  }

  clearAllFilters() {
    this.filterGroups.forEach(group => {
      group.options.forEach(option => {
        option.selected = false;
      });
      this.selectedFilters[group.id] = [];
    });
    this.emitFilterChange();
    this.clearAll.emit();
  }

  applyFilters() {
    this.apply.emit(this.selectedFilters);
    if (!this.showApplyButton) {
      this.isOpen = false;
      this.toggleFilter.emit(this.isOpen);
    }
  }

  private emitFilterChange() {
    this.filterChange.emit({ ...this.selectedFilters });
  }

  getActiveFiltersCount(): number {
    return Object.values(this.selectedFilters).reduce((total, filters) => total + filters.length, 0);
  }

  getSizeClasses(): string {
    const sizeMap = {
      'sm': 'filter-sm',
      'md': 'filter-md',
      'lg': 'filter-lg',
      'xlg': 'filter-xlg'
    };
    return sizeMap[this.size];
  }

  getCheckboxSize(): 'sm' | 'md' | 'lg' {
    const sizeMap = {
      'sm': 'sm' as const,
      'md': 'md' as const,
      'lg': 'md' as const,
      'xlg': 'lg' as const
    };
    return sizeMap[this.size];
  }

  getIconSize(): number {
    const sizeMap = {
      'sm': 12,
      'md': 16,
      'lg': 18,
      'xlg': 20
    };
    return sizeMap[this.size];
  }
}
