<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Drawer Component</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 40px;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        }
        h1 {
            color: #1a1a1a;
            margin-bottom: 20px;
        }
        .description {
            color: #6b7280;
            margin-bottom: 30px;
            line-height: 1.6;
        }
        .usage-example {
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        .code {
            background: #1a1a1a;
            color: #f8f8f2;
            padding: 15px;
            border-radius: 6px;
            font-family: 'Monaco', 'Men<PERSON>', 'Ubuntu Mono', monospace;
            font-size: 14px;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Test Drawer Component</h1>
        
        <div class="description">
            <p>This test component demonstrates the AVA Drawer Component with a design that exactly matches the provided reference image. The component includes:</p>
            <ul>
                <li>Custom close button in the header</li>
                <li>Proper typography and spacing</li>
                <li>Stats grid with inline SVG icons</li>
                <li>Pink description section</li>
                <li>Gradient action button</li>
            </ul>
        </div>

        <div class="usage-example">
            <h3>How to Use</h3>
            <p>Import and use the test drawer component in your Angular application:</p>
            
            <div class="code">
import { TestDrawerComponent } from './test-drawer';

@Component({
  imports: [TestDrawerComponent],
  template: `&lt;test-drawer&gt;&lt;/test-drawer&gt;`
})
export class AppComponent { }
            </div>
        </div>

        <div class="usage-example">
            <h3>Component Features</h3>
            <ul>
                <li><strong>Responsive Design:</strong> Adapts to different screen sizes</li>
                <li><strong>Accessibility:</strong> Proper ARIA labels and keyboard navigation</li>
                <li><strong>Visual Accuracy:</strong> Pixel-perfect match to reference design</li>
                <li><strong>Interactive Elements:</strong> Hover effects and smooth animations</li>
                <li><strong>Clean Code:</strong> Well-structured and maintainable implementation</li>
            </ul>
        </div>

        <div class="usage-example">
            <h3>Key Visual Elements</h3>
            <ul>
                <li><strong>Header:</strong> Title with custom close button</li>
                <li><strong>Subtitle:</strong> Descriptive text with proper color</li>
                <li><strong>Action Button:</strong> Pink outlined button with icon</li>
                <li><strong>Tags:</strong> Category tags with special accuracy styling</li>
                <li><strong>Stats Grid:</strong> 2x2 grid with icons and values</li>
                <li><strong>Description:</strong> Pink background section</li>
                <li><strong>Primary Action:</strong> Gradient button</li>
            </ul>
        </div>

        <p><strong>Note:</strong> To see the component in action, integrate it into your Angular application and click the trigger button to open the drawer.</p>
    </div>
</body>
</html>
