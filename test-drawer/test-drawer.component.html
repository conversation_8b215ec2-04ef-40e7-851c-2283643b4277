<div class="test-drawer-container">
  <!-- Trigger <PERSON> -->
  <div class="trigger-section">
    <h1>Drawer Component Test</h1>
    <p>Click the button below to open the drawer with the exact design from the provided image.</p>
    
    <ava-button 
      label="Open Component Card" 
      variant="primary" 
      size="large"
      (userClick)="openDrawer()">
    </ava-button>
  </div>

  <!-- Drawer Component -->
  <ava-drawer
    [isOpen]="isDrawerOpen"
    position="right"
    size="medium"
    [showOverlay]="true"
    [closeOnOverlayClick]="true"
    [closeOnEscape]="true"
    [showCloseButton]="true"
    [animate]="true"
    closeIcon="x"
    (opened)="onDrawerOpened()"
    (closed)="onDrawerClosed()">
    
    <!-- Component Card Content -->
    <div class="component-card">
      <!-- Header -->
      <div class="header">
        <h1 class="title">{{ componentData.title }}</h1>
      </div>

      <!-- Subtitle -->
      <p class="subtitle">{{ componentData.subtitle }}</p>

      <!-- Add to list button -->
      <ava-button 
        class="add-to-list-btn"
        variant="secondary"
        size="medium"
        iconName="plus"
        iconPosition="left"
        label="Add to my list"
        [outlined]="true"
        [pill]="true"
        (userClick)="onAddToList()">
      </ava-button>

      <!-- Tags -->
      <div class="tags">
        <span 
          *ngFor="let tag of componentData.tags" 
          class="tag"
          [class.accuracy-tag]="tag.type === 'accuracy'">
          {{ tag.label }}
        </span>
      </div>

      <!-- Agent ranking -->
      <div class="agent-ranking">
        <span class="ranking-text">{{ componentData.ranking }}</span>
      </div>

      <!-- Stats grid -->
      <div class="stats-grid">
        <div *ngFor="let stat of componentData.stats" class="stat-item">
          <div class="stat-label">{{ stat.label }}</div>
          <div class="stat-icon" *ngIf="stat.icon">
            <ava-icon 
              [iconName]="stat.icon" 
              [iconSize]="20" 
              iconColor="#6b7280">
            </ava-icon>
          </div>
          <div *ngIf="stat.value" class="stat-value">{{ stat.value }}</div>
          <div *ngIf="stat.value && stat.icon === 'star'" class="stat-rating">
            <span class="rating-number">{{ stat.value }}</span>
            <ava-icon 
              iconName="star" 
              [iconSize]="16" 
              iconColor="#fbbf24">
            </ava-icon>
          </div>
          <div class="stat-sublabel">{{ stat.sublabel }}</div>
        </div>
      </div>

      <!-- What it's for section -->
      <div class="description-section">
        <h2 class="section-title">{{ componentData.description.title }}</h2>
        <p class="description-text">{{ componentData.description.content }}</p>
      </div>

      <!-- Go to Playground button -->
      <ava-button 
        class="playground-btn"
        label="Go to Playground"
        variant="primary"
        size="large"
        width="100%"
        (userClick)="onGoToPlayground()">
      </ava-button>
    </div>
  </ava-drawer>
</div>
