<div class="test-drawer-container">
  <!-- Trigger <PERSON> -->
  <div class="trigger-section">
    <h1>Drawer Component Test</h1>
    <p>Click the button below to open the drawer with the exact design from the provided image.</p>

    <ava-button
      label="Open Component Card"
      variant="primary"
      size="large"
      (userClick)="openDrawer()">
    </ava-button>
  </div>

  <!-- Drawer Component -->
  <ava-drawer
    [isOpen]="isDrawerOpen"
    position="right"
    size="small"
    [showOverlay]="true"
    [closeOnOverlayClick]="true"
    [closeOnEscape]="true"
    [showCloseButton]="false"
    [animate]="true"
    (opened)="onDrawerOpened()"
    (closed)="onDrawerClosed()">

    <!-- Component Card Content -->
    <div class="component-card">
      <!-- Header with close button -->
      <div class="header">
        <h1 class="title">{{ componentData.title }}</h1>
        <button class="close-btn" (click)="closeDrawer()" aria-label="Close">
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <line x1="18" y1="6" x2="6" y2="18"></line>
            <line x1="6" y1="6" x2="18" y2="18"></line>
          </svg>
        </button>
      </div>

      <!-- Subtitle -->
      <p class="subtitle">{{ componentData.subtitle }}</p>

      <!-- Add to list button -->
      <ava-button
        class="add-to-list-btn"
        variant="secondary"
        size="medium"
        iconName="Plus"
        iconPosition="left"
        label="Add to my list"
        [outlined]="true"
        [pill]="true"
        (userClick)="onAddToList()">
      </ava-button>

      <!-- Tags -->
      <div class="tags">
        <span
          *ngFor="let tag of componentData.tags"
          class="tag"
          [class.accuracy-tag]="tag.type === 'accuracy'">
          {{ tag.label }}
        </span>
      </div>

      <!-- Agent ranking -->
      <div class="agent-ranking">
        <span class="ranking-text">{{ componentData.ranking }}</span>
      </div>

      <!-- Stats grid -->
      <div class="stats-grid">
        <div *ngFor="let stat of componentData.stats" class="stat-item">
          <div class="stat-label">{{ stat.label }}</div>

          <!-- Icon for Category and Developed by -->
          <div class="stat-icon" *ngIf="stat.icon && stat.icon !== 'Star'">
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <ng-container [ngSwitch]="stat.icon">
                <!-- Code icon for Category -->
                <g *ngSwitchCase="'Code'">
                  <polyline points="16 18 22 12 16 6"></polyline>
                  <polyline points="8 6 2 12 8 18"></polyline>
                </g>
                <!-- User icon for Developed by -->
                <g *ngSwitchCase="'User'">
                  <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
                  <circle cx="12" cy="7" r="4"></circle>
                </g>
              </ng-container>
            </svg>
          </div>

          <!-- Value for Relevancy -->
          <div *ngIf="stat.value && stat.icon !== 'Star'" class="stat-value">{{ stat.value }}</div>

          <!-- Rating with star -->
          <div *ngIf="stat.value && stat.icon === 'Star'" class="stat-rating">
            <span class="rating-number">{{ stat.value }}</span>
            <svg width="16" height="16" viewBox="0 0 24 24" fill="#fbbf24">
              <polygon points="12,2 15.09,8.26 22,9.27 17,14.14 18.18,21.02 12,17.77 5.82,21.02 7,14.14 2,9.27 8.91,8.26"></polygon>
            </svg>
          </div>

          <div class="stat-sublabel">{{ stat.sublabel }}</div>
        </div>
      </div>

      <!-- What it's for section -->
      <div class="description-section">
        <h2 class="section-title">{{ componentData.description.title }}</h2>
        <p class="description-text">{{ componentData.description.content }}</p>
      </div>

      <!-- Go to Playground button -->
      <ava-button
        class="playground-btn"
        label="Go to Playground"
        variant="primary"
        size="large"
        width="100%"
        (userClick)="onGoToPlayground()">
      </ava-button>
    </div>
  </ava-drawer>
</div>
